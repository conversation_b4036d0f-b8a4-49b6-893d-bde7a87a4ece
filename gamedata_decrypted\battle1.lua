--[[

金庸群侠传X 战斗逻辑扩展



]]--



local Tools = luanet.import_type('JyGame.Tools')

local Debug = luanet.import_type('UnityEngine.Debug')

local LuaTool = luanet.import_type('JyGame.LuaTool')

local CommonSettings = luanet.import_type('JyGame.CommonSettings')

local RuntimeData = luanet.import_type('JyGame.RuntimeData')

local Color = luanet.import_type('UnityEngine.Color')



local Item = luanet.import_type('JyGame.Item')

local ItemInstance = luanet.import_type('JyGame.ItemInstance')

--local RuntimeData = luanet.import_type('JyGame.RuntimeData')









makecz=function(sk)

  local new=Item()

  new.Name=sk.."残章"

  new.type = 10

  new.pic = "物品.残章"

  new.CanzhangSkill = sk

  new.desc = "【稀有】神秘的武学残章，能够永久提高"..sk.."的等级上限1级。\n注：本物品将被自动使用，提高全存档的该项武学等级上限。"

  new.price = 200

  return new

end





function BATTLE_EndBattle(battlefield, bonusLogic)



  --基本泛用条件语句

    if bonusLogic.Win==true then

--if bonusLogic.Battle.Key=="测试地图1" then

--bonusLogic.Items:Add(Item.GetItem("碧水剑"):Generate(true))

--bonusLogic.Money=bonusLogic.Money+2023 --把结算银两增加2023

--bonusLogic.Exp=bonusLogic.Exp*10 --把结算经验值翻十倍

--end

	local bbkey = bonusLogic.Battle.Key

  	if bbkey=="嵩山弟子_战斗" then

  		bonusLogic.Exp=bonusLogic.Exp*1.5

  	end

  	if bbkey=="嵩山高手_战斗" then

  		bonusLogic.Exp=bonusLogic.Exp*1.5

  	end

  	if bbkey=="华山弟子_战斗" then

  		bonusLogic.Exp=bonusLogic.Exp*1.5

  	end

  	if bbkey=="华山高手_战斗" then

  		bonusLogic.Exp=bonusLogic.Exp*1.5

  	end

  	if bbkey=="主角对战恒山弟子" then

  		bonusLogic.Exp=bonusLogic.Exp*2.0

  	end

  	if bbkey=="沙漠战斗1" then

  		bonusLogic.Exp=bonusLogic.Exp*2.5

  	end

  	if bbkey=="沙漠战斗2" then

  		bonusLogic.Exp=bonusLogic.Exp*2.5

  	end

  	if bbkey=="沙漠战斗3" then

  		bonusLogic.Exp=bonusLogic.Exp*2.5

  	end

  	if bbkey=="沙漠战斗4" then

  		bonusLogic.Exp=bonusLogic.Exp*2.5

  	end

  	if bbkey=="星宿练级战斗" then

  		bonusLogic.Exp=bonusLogic.Exp*2.5

  	end

  	if bbkey=="梅芳姑陪练" then

  		bonusLogic.Exp=bonusLogic.Exp*2

  	end

  	if bbkey=="周伯通_练级" then

  		bonusLogic.Exp=bonusLogic.Exp*2.5

  	end

  	if bbkey=="少林练级战斗" then

  		bonusLogic.Exp=bonusLogic.Exp*2

  	end

  	if bbkey=="少林练级战斗" then

  		bonusLogic.Exp=bonusLogic.Exp*2

  	end

  end

  --特征条件语句

--if string.sub(bonusLogic.Battle.Key,a,b)=="战斗名特征标记" then

--执行内容

--end

-- ]]

--bonusLogic.Yuanbao=0 --对结算元宝直接直接赋值

--bonusLogic.Money=bonusLogic.Money+2023 --把结算银两增加2023

--bonusLogic.Exp=bonusLogic.Exp*10 --把结算经验值翻十倍

--结算掉落道具今天只讲新增变更 怎么删除你们自己玩

--我估计你们处理不好新增就先把格式告诉你们

--新增常规道具掉落写法

--bonusLogic.Items:Add(Item.GetItem("常规道具名"):Generate(false))

--bonusLogic.Items:Add(ItemInstance.Generate("常规道具名",false))

--新增带属性的装备掉落写法

--bonusLogic.Items:Add(Item.GetItem("装备道具名"):Generate(true))

--bonusLogic.Items:Add(ItemInstance.Generate("装备道具名",true))

--新增指定残章掉落写法

--这样写是因为残章道具并不是在items.xml中定义的

--所以常规写法弄不出来残章

--bonusLogic.Items:Add(makecz("被指定的残章技能名"):Generate(false))



end











--在某个角色行动之前调用

function BATTLE_BeforeRoleAction(battlefield, currentSprite)

	--currentSprite:AttackInfo("轮到我行动啦!", Color.black)

	

	if(currentSprite.Role:HasTalent("狠毒") and Tools.ProbabilityTest(0.3)) then

		currentSprite:AddBuff("攻击强化",3,20)

	end

	if(currentSprite.Role:HasTalent("绿毛龟") and Tools.ProbabilityTest(0.1)) then

		currentSprite:AddBuff("伤害加深",3,20)

	end

	if(currentSprite.Role:HasTalent("孕妇") and Tools.ProbabilityTest(0.9)) then

		currentSprite:AddBuff("伤害加深",3,99)

		currentSprite:AddBuff("内伤",3,99)

		currentSprite:AddBuff("缓速",1,99)

	end



	if currentSprite.Role:HasTalent("素问天枢") and currentSprite.Hp<=currentSprite.MaxHp*0.9 then

  		currentSprite.Hp=currentSprite.Hp+math.ceil(currentSprite.MaxHp*0.1)

	end

	if currentSprite.Role:HasTalent("太阴真气") and currentSprite.Mp<=currentSprite.MaxMp*0.9 then

  		currentSprite.Mp=currentSprite.Mp+math.ceil(currentSprite.MaxMp*0.1)

	end

	if currentSprite.Role:HasTalent("内功大师") and currentSprite.Mp<=currentSprite.MaxMp*0.9 then

  		currentSprite.Mp=currentSprite.Mp+math.ceil(currentSprite.MaxMp*0.01)

	end

	if currentSprite.Role:HasTalent("生机勃勃") and currentSprite.Hp<=currentSprite.MaxHp*0.9 then

  		currentSprite.Hp=currentSprite.Hp+math.ceil(currentSprite.MaxHp*0.01)

	end

	if currentSprite.Role:HasTalent("搔首弄姿") and Tools.ProbabilityTest(0.3) then

  	if currentSprite:AddBuffOnly2("定身",0,1)==true then

    		currentSprite:AttackInfo("哎呀……别盯着看我的臀部……",Color.magenta)

  	end

end

	if(currentSprite:HasBuff("魅影绝世"))  then

			    local x1 = math.floor(Tools.GetRandomInt(-6,6))

		        local y1 = math.floor(Tools.GetRandomInt(-2,2))

                local   x = currentSprite.x + x1 

                local    y = currentSprite.y + y1

                if (x > 10) then

                x = 10

                end

                if (x < 0) then

                x = 0

                end

                if (y > 3) then

                y = 3

                end

                if (y < 0) then

                y = 0

                end

if battlefield:GetSprite(x,y) == nil then

		currentSprite:SetPos(x,y)

currentSprite:AttackInfo("我就是来无影去无踪！", Color.black)

currentSprite:DeleteBuff("魅影绝世")

	    end

	end

end





--[[角色施展技能之后(播放完技能攻击动画）调用

function BATTLE_AfterSkillAnimation(battlefield, currentSprite, skill, hitnumber)

	--currentSprite:AttackInfo("我打中了"..hitnumber.."个人！", Color.red)

	

	--例子天赋：赚便宜，如果同时打中4个人以上，恢复最大生命的10%

	if(currentSprite.Role:HasTalent("赚便宜") and hitnumber>=4) then

		local hpadd = math.ceil(currentSprite.MaxHp * 0.1)

		currentSprite.Hp = currentSprite.Hp + hpadd

		currentSprite:AttackInfo("+" .. hpadd, Color.green)

		currentSprite:RandomSay(LuaTool.MakeStringArray({"哈哈，这便宜赚得爽!", "有便宜就要赚！"}))

	end

	

end]]



--角色技能命中目标后（播放技能攻击动画之前）调用

--hitnumber : 命中目标个数

function BATTLE_BeforeSkillAnimation(battlefield, currentSprite, skill, hitnumber)

	--currentSprite:AttackInfo("我打中了"..hitnumber.."个人！", Color.white)

	

	--例子天赋：空挥狂魔，没有打中人的情况下，自动攻击最近的一个人敌人

	if(currentSprite.Role:HasTalent("空挥狂魔") and hitnumber == 0) then

		--寻找最近的敌人

		mindist = 9999

		findSprite = nil

		for _,sprite in pairs(battlefield.SpritesTable) do

			if sprite.Team ~= currentSprite.Team then

				local distx = math.abs(currentSprite.X - sprite.X)

				local disty = math.abs(currentSprite.Y - sprite.Y)

				local dist = distx + disty

				if(dist < mindist) then

					mindist = dist

					findSprite = sprite

				end

			end

		end

		

		--如果找到了，攻击他

		if(findSprite ~= nil) then

			battlefield:Attack(currentSprite, skill, findSprite.X, findSprite.Y)

		end

	end



	if currentSprite.Role:HasTalent("赵日天不服") and hitnumber<=2 then

local mindist = 10

local findSprite ={}

		for _,sprite in pairs(battlefield.SpritesTable) do

			if sprite.Team ~= currentSprite.Team then

				local distx = math.abs(currentSprite.X - sprite.X)

				local disty = math.abs(currentSprite.Y - sprite.Y)

				local dist = math.max(distx,disty)

if(dist < mindist) then

table.insert(findSprite,sprite)

end

end

end

		

  if(#findSprite>0) then

for findindex,findvalue in pairs(findSprite) do

battlefield:Attack(currentSprite, skill, findvalue.X, findvalue.Y)

battlefield:Log("战神附体！【"..currentSprite.Role.Name.."】使用【"..skill.Name.."】对【"..findvalue.Role.Name.."】发动了【强制攻击】")

currentSprite:AttackInfo("老子还要继续日天", Color.black)

end

end

end



if(currentSprite.Role:HasTalent("良辰必有重谢") and hitnumber<=2) then

local mindistg = 1		

local findSpriteg = nil

		for _,spriteg in pairs(battlefield.SpritesTable) do

			if spriteg.Team ~= currentSprite.Team then

local distgx = math.abs(currentSprite.X - spriteg.X)

local distgy = math.abs(currentSprite.Y - spriteg.Y)

local distg = tonumber(distgx*distgy)

				if(distg==0) then

					--mindistg= distg 不控制继续循环

					findSpriteg = spriteg				

		end

end

		if(findSpriteg ~= nil) then

battlefield:Attack(currentSprite, skill, findSpriteg.X, findSpriteg.Y)

findSpriteg:AttackInfo("真是无妄之灾", Color.red)

currentSprite:AttackInfo("纵横天下", Color.black)

end

end

end

end



--角色濒死时调用

function BATTLE_Die(battlefield, sprite, attackResult)

local isDc=false

local findSprite1 = nil

for _,sprite1 in pairs(battlefield.SpritesTable) do

if  (sprite1.Role:HasTalent("神之妙手") and sprite1.Team == sprite.Team and sprite1~=sprite) then

findSprite1=sprite1

isDc=true

end

end



if isDc==true then

if(Tools.ProbabilityTest(0.99)) then 

battlefield:Log("神医【"..findSprite1.Role.Name.."】对【"..sprite.Role.Name.."】发动了【神之妙手】")

sprite:Say("谢谢神医相助")

sprite.Hp = sprite.MaxHp*0.8

sprite.Mp = sprite.MaxMp*0.5

sprite:Refresh()

return false

end

end

	--天赋“至死不渝”， 80%概率不死，剩1滴血

	if(sprite.Role:HasTalent("至死不渝") and Tools.ProbabilityTest(0.8)) then

		sprite:Say("就是死不了，你打我啊")

		sprite.Hp = spriteMaxHp

		return false

	end



	return false 

end



--角色休息时调用

function BATTLE_Rest(battlefield, sprite, hprecover, mprecover)

	--sprite:Say("我在休息...")

	

    if(sprite.Role:HasTalent("淫猥下流")) then		

         hprecover = hprecover+math.floor((sprite.MaxHp-sprite.Hp)*0.1)

         sprite:Say("还想要……还想要更多……")

        if(Tools.ProbabilityTest(0.1)) then

			sprite:AddBuff("晕眩",2,2)

--			sprite:Say("啊……好舒服……")

		end

	end

	

	--固定返回格式

	return string.format("%s,%s", hprecover, mprecover)

end

