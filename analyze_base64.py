#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Base64字符串
"""

import base64
import string

def analyze_base64_string():
    # 读取skill.lua
    with open("gamedata/modcache/HYYM/lua/skill.lua", 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    # 提取加密部分
    parts = content.split('@', 1)
    encrypted_str = parts[1]
    
    # 按照处理步骤
    processed = encrypted_str.replace('\\', '0').replace('_', '1')
    processed = processed[1:-1]
    processed = processed.replace("/", "").replace("#", "/")
    
    print(f"处理后字符串长度: {len(processed)}")
    print(f"长度模4: {len(processed) % 4}")
    
    # 检查字符组成
    valid_base64_chars = set(string.ascii_letters + string.digits + '+/=')
    invalid_chars = set()
    
    for char in processed:
        if char not in valid_base64_chars:
            invalid_chars.add(char)
    
    print(f"无效Base64字符: {invalid_chars}")
    
    # 统计字符频率
    char_count = {}
    for char in processed:
        char_count[char] = char_count.get(char, 0) + 1
    
    # 显示最常见的字符
    sorted_chars = sorted(char_count.items(), key=lambda x: x[1], reverse=True)
    print("最常见的字符:")
    for char, count in sorted_chars[:20]:
        print(f"  '{char}': {count}")
    
    # 尝试不同的填充方式
    print("\n尝试不同的填充方式...")

    # 方法1：添加填充到4的倍数
    test_str = processed
    while len(test_str) % 4 != 0:
        test_str += '='

    print(f"方法1 - 添加填充后长度: {len(test_str)}")
    try:
        decoded = base64.b64decode(test_str)
        print(f"方法1解码成功！字节长度: {len(decoded)}")
    except Exception as e:
        print(f"方法1解码失败: {e}")

    # 方法2：移除最后一个字符然后填充
    test_str2 = processed[:-1]
    while len(test_str2) % 4 != 0:
        test_str2 += '='

    print(f"方法2 - 移除最后字符后长度: {len(test_str2)}")
    try:
        decoded = base64.b64decode(test_str2)
        print(f"方法2解码成功！字节长度: {len(decoded)}")

        # 尝试解码为字符串
        for encoding in ['utf-8', 'latin-1', 'cp1252']:
            try:
                decoded_str = decoded.decode(encoding)
                print(f"使用{encoding}编码成功，字符串长度: {len(decoded_str)}")
                print(f"前100字符: {repr(decoded_str[:100])}")

                # 保存结果
                with open(f"skill_method2_{encoding}.txt", 'w', encoding='utf-8') as f:
                    f.write(decoded_str)
                print(f"保存到: skill_method2_{encoding}.txt")
                return decoded_str
            except Exception as e:
                print(f"使用{encoding}编码失败: {e}")

    except Exception as e:
        print(f"方法2解码失败: {e}")

    # 方法3：移除前面几个字符
    for i in range(1, 4):
        test_str3 = processed[i:]
        while len(test_str3) % 4 != 0:
            test_str3 += '='

        print(f"方法3.{i} - 移除前{i}个字符后长度: {len(test_str3)}")
        try:
            decoded = base64.b64decode(test_str3)
            print(f"方法3.{i}解码成功！字节长度: {len(decoded)}")
        except Exception as e:
            print(f"方法3.{i}解码失败: {e}")

    return None
    
    # 检查前后几个字符
    print(f"\n前50字符: {repr(processed[:50])}")
    print(f"后50字符: {repr(processed[-50:])}")

if __name__ == "__main__":
    analyze_base64_string()
