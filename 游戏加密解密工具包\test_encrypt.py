#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试加密功能
"""

import os
import sys
from game_crypto_tool import GameCryptoTool

def test_encrypt_decrypt():
    """测试加密解密功能"""
    
    # 创建工具实例
    tool = GameCryptoTool()
    
    # 测试文本
    test_content = """--[[

金庸群侠传X 测试脚本

]]--

local Tools = luanet.import_type('JyGame.Tools')
local Debug = luanet.import_type('UnityEngine.Debug')

function test_function()
    Debug.Log("这是一个测试函数")
    return true
end

-- 测试注释
print("Hello World!")
"""
    
    print("=== 测试加密功能 ===")
    
    # 1. 加密测试内容
    encrypted_result, error = tool.encrypt_file(test_content)
    if error:
        print(f"❌ 加密失败: {error}")
        return False
    
    print(f"✅ 加密成功")
    print(f"原始长度: {len(test_content)} 字符")
    print(f"加密后长度: {len(encrypted_result)} 字符")
    print(f"加密结果前100字符: {encrypted_result[:100]}...")
    
    # 保存加密结果
    with open("test_encrypted.lua", 'w', encoding='utf-8') as f:
        f.write(encrypted_result)
    print("✅ 加密结果已保存到: test_encrypted.lua")
    
    print("\n=== 测试解密功能 ===")
    
    # 2. 解密测试
    decrypted_result, error = tool.decrypt_file("test_encrypted.lua")
    if error:
        print(f"❌ 解密失败: {error}")
        return False
    
    print(f"✅ 解密成功")
    print(f"解密后长度: {len(decrypted_result)} 字符")
    
    # 保存解密结果
    with open("test_decrypted.lua", 'w', encoding='utf-8') as f:
        f.write(decrypted_result)
    print("✅ 解密结果已保存到: test_decrypted.lua")
    
    print("\n=== 验证结果 ===")
    
    # 3. 验证结果是否一致
    if test_content == decrypted_result:
        print("✅ 加密解密测试通过！原始内容与解密后内容完全一致")
        return True
    else:
        print("❌ 加密解密测试失败！内容不一致")
        print(f"原始内容长度: {len(test_content)}")
        print(f"解密内容长度: {len(decrypted_result)}")
        
        # 找出差异
        for i, (a, b) in enumerate(zip(test_content, decrypted_result)):
            if a != b:
                print(f"第一个差异在位置 {i}: 原始='{a}' 解密='{b}'")
                break
        
        return False

def test_real_file():
    """测试真实文件的重新加密"""
    print("\n=== 测试真实文件重新加密 ===")
    
    # 使用之前解密的文件
    decrypted_file = "skill_final_decrypted.lua"
    if not os.path.exists(decrypted_file):
        print(f"❌ 找不到解密文件: {decrypted_file}")
        return False
    
    tool = GameCryptoTool()
    
    # 读取解密后的内容
    with open(decrypted_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"📁 读取文件: {decrypted_file}")
    print(f"📏 文件长度: {len(content)} 字符")
    
    # 重新加密
    encrypted_result, error = tool.encrypt_file(content)
    if error:
        print(f"❌ 重新加密失败: {error}")
        return False
    
    # 保存重新加密的结果
    with open("skill_reencrypted.lua", 'w', encoding='utf-8') as f:
        f.write(encrypted_result)
    
    print("✅ 重新加密成功，保存到: skill_reencrypted.lua")
    
    # 验证重新加密的文件能否正确解密
    decrypted_again, error = tool.decrypt_file("skill_reencrypted.lua")
    if error:
        print(f"❌ 重新解密失败: {error}")
        return False
    
    if content == decrypted_again:
        print("✅ 重新加密测试通过！")
        return True
    else:
        print("❌ 重新加密测试失败！")
        return False

if __name__ == "__main__":
    print("开始测试加密解密功能...\n")
    
    # 测试基本功能
    success1 = test_encrypt_decrypt()
    
    # 测试真实文件
    success2 = test_real_file()
    
    print(f"\n=== 测试总结 ===")
    print(f"基本功能测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"真实文件测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("🎉 所有测试都通过了！工具可以正常使用。")
    else:
        print("⚠️ 部分测试失败，请检查问题。")
