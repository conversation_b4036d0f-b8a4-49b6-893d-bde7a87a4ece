#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版游戏解密工具
基于对C#代码的完整分析
"""

import os
import sys
import base64
import hashlib
from Crypto.Cipher import DES3
from Crypto.Util.Padding import unpad
import argparse

class GameDecryptor:
    def __init__(self):
        # 从SaveManager代码中提取的密钥
        self.des3_key = hashlib.md5("Yh$45Ct@mods".encode('utf-8')).digest()
        
    def extract_string(self, encrypted_str):
        """
        对应SaveManager.ExtractString方法
        基于成功的解密经验重新实现
        """
        try:
            print(f"原始字符串长度: {len(encrypted_str)}")

            # 检查是否有长度前缀
            expected_length = None
            if '@' in encrypted_str:
                parts = encrypted_str.split('@', 1)
                if len(parts) == 2 and parts[0].isdigit():
                    expected_length = int(parts[0])
                    encrypted_str = parts[1]
                    print(f"预期解密长度: {expected_length}")

            # 直接Base64解码（不需要字符替换等处理）
            try:
                # 添加填充
                padded_str = encrypted_str
                while len(padded_str) % 4 != 0:
                    padded_str += '='

                raw_data = base64.b64decode(padded_str)
                print(f"Base64解码成功: {len(raw_data)} 字节")
            except Exception as e:
                print(f"Base64解码失败: {e}")
                return None

            # 3DES解密
            try:
                # 确保长度是8的倍数
                if len(raw_data) % 8 != 0:
                    truncated_length = (len(raw_data) // 8) * 8
                    raw_data = raw_data[:truncated_length]
                    print(f"截断到8的倍数: {len(raw_data)} 字节")

                cipher = DES3.new(self.des3_key, DES3.MODE_ECB)
                decrypted_data = cipher.decrypt(raw_data)
                print(f"3DES解密成功: {len(decrypted_data)} 字节")

                # 尝试移除填充
                try:
                    from Crypto.Util.Padding import unpad
                    unpadded_data = unpad(decrypted_data, 8)
                    print(f"移除填充后: {len(unpadded_data)} 字节")
                    decrypted_data = unpadded_data
                except:
                    print("无法移除填充，使用原始数据")

            except Exception as e:
                print(f"3DES解密失败: {e}")
                return None

            # 解码为字符串
            try:
                final_result = decrypted_data.decode('utf-8')
                print(f"UTF-8解码成功: {len(final_result)} 字符")

                if expected_length and len(final_result) != expected_length:
                    print(f"警告：解密长度({len(final_result)})与预期长度({expected_length})不匹配")

                return final_result

            except Exception as e:
                print(f"UTF-8解码失败: {e}")
                return None

        except Exception as e:
            print(f"ExtractString解密失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def decrypt_file(self, file_path, output_path=None):
        """
        解密文件
        """
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return False
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                
            print(f"处理文件: {file_path}")
            print(f"文件大小: {len(content)} 字符")
            
            decrypted = None
            
            # 判断文件类型并选择解密方法
            if content.startswith(('@', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9')) and '@' in content:
                # 可能是ExtractString加密的文件
                print("尝试ExtractString解密...")
                decrypted = self.extract_string(content)
                
            if decrypted is None:
                print("解密失败")
                return False
                
            # 确定输出文件路径
            if output_path is None:
                base_name = os.path.splitext(file_path)[0]
                ext = os.path.splitext(file_path)[1]
                output_path = f"{base_name}_decrypted{ext}"
                
            # 写入解密后的内容
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(decrypted)
                
            print(f"解密成功: {output_path}")
            return True
            
        except Exception as e:
            print(f"解密过程出错: {e}")
            return False
    
    def decrypt_directory(self, dir_path, output_dir=None):
        """
        批量解密目录中的文件
        """
        if not os.path.exists(dir_path):
            print(f"目录不存在: {dir_path}")
            return
            
        if output_dir is None:
            output_dir = dir_path + "_decrypted"
            
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        success_count = 0
        total_count = 0
        
        for root, dirs, files in os.walk(dir_path):
            for file in files:
                if file.endswith(('.lua', '.xml')):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, dir_path)
                    output_file = os.path.join(output_dir, rel_path)
                    
                    # 创建输出目录
                    output_file_dir = os.path.dirname(output_file)
                    if not os.path.exists(output_file_dir):
                        os.makedirs(output_file_dir)
                    
                    total_count += 1
                    print(f"\n处理文件: {rel_path}")
                    
                    if self.decrypt_file(file_path, output_file):
                        success_count += 1
                        
        print(f"\n批量解密完成: {success_count}/{total_count} 个文件解密成功")

def main():
    parser = argparse.ArgumentParser(description='游戏文件解密工具')
    parser.add_argument('input', help='输入文件或目录路径')
    parser.add_argument('-o', '--output', help='输出文件或目录路径')
    parser.add_argument('-d', '--directory', action='store_true', help='批量处理目录')
    
    args = parser.parse_args()
    
    decryptor = GameDecryptor()
    
    if args.directory:
        decryptor.decrypt_directory(args.input, args.output)
    else:
        decryptor.decrypt_file(args.input, args.output)

if __name__ == "__main__":
    if len(sys.argv) == 1:
        # 如果没有参数，测试skill.lua
        decryptor = GameDecryptor()
        decryptor.decrypt_file("gamedata/modcache/HYYM/lua/skill.lua", "skill_final_decrypted.lua")
    else:
        main()
