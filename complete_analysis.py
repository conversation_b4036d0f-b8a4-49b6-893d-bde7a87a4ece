#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整分析C#代码逻辑
重新实现整个解密流程
"""

import base64
import hashlib
from Crypto.Cipher import DES3
from Crypto.Util.Padding import unpad

def analyze_csharp_logic():
    """
    重新分析C#代码逻辑
    
    从SaveManager.ExtractString方法:
    1. str.Replace('\\', '0').Replace('_', '1')
    2. .Substring(1, str.Length - 2)
    3. .Replace("/", "").Replace("#", "/")
    4. Decode(Encoding.UTF8, processed_string)
    5. 字符重复解压缩
    
    Decode方法可能是Base64解码
    """
    
    # 读取原始文件
    with open("gamedata/modcache/HYYM/lua/skill.lua", 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    print(f"原始内容长度: {len(content)}")
    print(f"原始内容: {content[:100]}...")
    
    # 分离长度前缀
    if '@' in content:
        parts = content.split('@', 1)
        expected_length = int(parts[0])
        encrypted_str = parts[1]
        print(f"预期长度: {expected_length}")
        print(f"加密字符串长度: {len(encrypted_str)}")
    else:
        print("没有找到长度前缀")
        return
    
    # 步骤1: 替换特殊字符
    step1 = encrypted_str.replace('\\', '0').replace('_', '1')
    print(f"步骤1后长度: {len(step1)}")
    
    # 步骤2: 移除首尾字符
    step2 = step1[1:-1]
    print(f"步骤2后长度: {len(step2)}")
    
    # 步骤3: 处理路径分隔符
    step3 = step2.replace("/", "").replace("#", "/")
    print(f"步骤3后长度: {len(step3)}")
    
    # 步骤4: Base64解码 - 尝试不同的方法
    print("\n=== 尝试Base64解码 ===")
    
    # 方法1: 直接解码
    try:
        decoded1 = base64.b64decode(step3 + '===')  # 添加最大填充
        print(f"方法1成功: {len(decoded1)} 字节")
    except Exception as e:
        print(f"方法1失败: {e}")
        decoded1 = None
    
    # 方法2: 移除最后一个字符
    try:
        decoded2 = base64.b64decode(step3[:-1] + '===')
        print(f"方法2成功: {len(decoded2)} 字节")
    except Exception as e:
        print(f"方法2失败: {e}")
        decoded2 = None
    
    # 方法3: 移除最后几个字符直到能解码
    decoded3 = None
    for i in range(1, 10):
        try:
            test_str = step3[:-i]
            while len(test_str) % 4 != 0:
                test_str += '='
            decoded3 = base64.b64decode(test_str)
            print(f"方法3.{i}成功: {len(decoded3)} 字节")
            break
        except:
            continue
    
    if decoded3 is None:
        print("方法3失败")
    
    # 选择成功的解码结果
    decoded_data = decoded2 or decoded3 or decoded1
    if decoded_data is None:
        print("所有Base64解码方法都失败")
        return
    
    print(f"\n使用解码数据: {len(decoded_data)} 字节")
    
    # 步骤5: 尝试不同的字符串解码
    print("\n=== 尝试字符串解码 ===")
    
    decoded_str = None
    for encoding in ['utf-8', 'latin-1', 'cp1252', 'ascii']:
        try:
            decoded_str = decoded_data.decode(encoding)
            print(f"{encoding}成功: {len(decoded_str)} 字符")
            print(f"前50字符: {repr(decoded_str[:50])}")
            
            # 保存中间结果
            with open(f"step5_{encoding}.txt", 'w', encoding='utf-8') as f:
                f.write(decoded_str)
            
            break
        except Exception as e:
            print(f"{encoding}失败: {e}")
    
    if decoded_str is None:
        print("所有字符串解码都失败")
        return
    
    # 步骤6: 字符重复解压缩
    print("\n=== 尝试字符重复解压缩 ===")
    
    result = []
    i = 0
    valid_pairs = 0
    invalid_pairs = 0
    
    while i < len(decoded_str) - 1:
        char = decoded_str[i]
        count_char = decoded_str[i + 1]
        count = ord(count_char) - ord('0')
        
        if 0 <= count <= 9:
            result.append(char * count)
            valid_pairs += 1
            if valid_pairs <= 10:  # 只显示前几个
                print(f"有效对{valid_pairs}: '{char}' * {count} = {repr(char * count)}")
        else:
            invalid_pairs += 1
            if invalid_pairs <= 10:  # 只显示前几个
                print(f"无效对{invalid_pairs}: '{char}' + '{count_char}' (count={count})")
        
        i += 2
    
    print(f"有效对数: {valid_pairs}, 无效对数: {invalid_pairs}")
    
    if valid_pairs > 0:
        final_result = ''.join(result)
        print(f"解压缩结果长度: {len(final_result)}")
        print(f"预期长度: {expected_length}")
        
        if len(final_result) == expected_length:
            print("✓ 长度匹配！")
        else:
            print("✗ 长度不匹配")
        
        # 检查是否包含Lua代码
        if any(keyword in final_result for keyword in ['function', 'local', 'end', 'if', 'then']):
            print("✓ 发现Lua关键字！")
        else:
            print("✗ 未发现Lua关键字")
        
        # 保存最终结果
        with open("skill_final_result.lua", 'w', encoding='utf-8') as f:
            f.write(final_result)
        print("最终结果保存到: skill_final_result.lua")
        
        return final_result
    else:
        print("没有有效的字符对，解压缩失败")
        return None

if __name__ == "__main__":
    analyze_csharp_logic()
