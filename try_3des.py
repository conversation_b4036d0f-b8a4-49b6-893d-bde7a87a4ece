#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
尝试3DES解密
"""

import hashlib
from Crypto.Cipher import DES3
from Crypto.Util.Padding import unpad
import base64

def try_3des_decrypt():
    # 从SaveManager代码中提取的密钥
    des3_key = hashlib.md5("Yh$45Ct@mods".encode('utf-8')).digest()
    print(f"3DES密钥: {des3_key.hex()}")
    
    # 读取Base64解码后的数据
    with open("skill_method2_latin-1.txt", 'rb') as f:
        encrypted_data = f.read()
    
    print(f"加密数据长度: {len(encrypted_data)}")
    print(f"前20字节: {encrypted_data[:20].hex()}")
    
    try:
        # 尝试3DES解密
        cipher = DES3.new(des3_key, DES3.MODE_ECB)
        
        # 确保数据长度是8的倍数（DES块大小）
        if len(encrypted_data) % 8 != 0:
            print(f"数据长度不是8的倍数，需要填充。当前长度: {len(encrypted_data)}")
            # 尝试截断到8的倍数
            truncated_length = (len(encrypted_data) // 8) * 8
            encrypted_data = encrypted_data[:truncated_length]
            print(f"截断后长度: {len(encrypted_data)}")
        
        decrypted_data = cipher.decrypt(encrypted_data)
        print(f"3DES解密成功！解密数据长度: {len(decrypted_data)}")
        
        # 尝试移除填充
        try:
            unpadded_data = unpad(decrypted_data, 8)
            print(f"移除填充后长度: {len(unpadded_data)}")
            decrypted_data = unpadded_data
        except:
            print("无法移除填充，使用原始解密数据")
        
        # 尝试不同编码
        for encoding in ['utf-8', 'latin-1', 'cp1252']:
            try:
                decoded_str = decrypted_data.decode(encoding)
                print(f"使用{encoding}编码成功")
                print(f"前100字符: {repr(decoded_str[:100])}")
                
                # 保存结果
                with open(f"skill_3des_{encoding}.txt", 'w', encoding='utf-8') as f:
                    f.write(decoded_str)
                print(f"保存到: skill_3des_{encoding}.txt")
                
                # 检查是否包含Lua代码特征
                if 'function' in decoded_str or 'local' in decoded_str or 'end' in decoded_str:
                    print("✓ 发现Lua代码特征！")
                    return decoded_str
                else:
                    print("✗ 未发现明显的Lua代码特征")
                    
            except Exception as e:
                print(f"使用{encoding}编码失败: {e}")
        
        return None
        
    except Exception as e:
        print(f"3DES解密失败: {e}")
        return None

def try_different_modes():
    """尝试不同的3DES模式"""
    des3_key = hashlib.md5("Yh$45Ct@mods".encode('utf-8')).digest()
    
    with open("skill_method2_latin-1.txt", 'rb') as f:
        encrypted_data = f.read()
    
    # 截断到8的倍数
    truncated_length = (len(encrypted_data) // 8) * 8
    encrypted_data = encrypted_data[:truncated_length]
    
    modes = [
        ('ECB', DES3.MODE_ECB),
        ('CBC', DES3.MODE_CBC),
    ]
    
    for mode_name, mode in modes:
        try:
            print(f"\n尝试{mode_name}模式...")
            
            if mode == DES3.MODE_CBC:
                # CBC模式需要IV，尝试使用前8字节作为IV
                if len(encrypted_data) >= 16:
                    iv = encrypted_data[:8]
                    data = encrypted_data[8:]
                    cipher = DES3.new(des3_key, mode, iv=iv)
                else:
                    continue
            else:
                cipher = DES3.new(des3_key, mode)
                data = encrypted_data
            
            decrypted_data = cipher.decrypt(data)
            
            # 尝试UTF-8解码
            try:
                decoded_str = decrypted_data.decode('utf-8')
                print(f"{mode_name}模式UTF-8解码成功")
                print(f"前100字符: {repr(decoded_str[:100])}")
                
                if 'function' in decoded_str or 'local' in decoded_str or 'end' in decoded_str:
                    print(f"✓ {mode_name}模式发现Lua代码特征！")
                    with open(f"skill_3des_{mode_name.lower()}.txt", 'w', encoding='utf-8') as f:
                        f.write(decoded_str)
                    return decoded_str
                    
            except:
                print(f"{mode_name}模式UTF-8解码失败")
                
        except Exception as e:
            print(f"{mode_name}模式失败: {e}")
    
    return None

if __name__ == "__main__":
    print("=== 尝试3DES解密 ===")
    result = try_3des_decrypt()
    
    if result is None:
        print("\n=== 尝试不同的3DES模式 ===")
        result = try_different_modes()
    
    if result is None:
        print("\n所有3DES解密尝试都失败了")
