#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试解密工具
"""

import os
from decrypt_tool import GameDecryptor

def test_main_lua():
    """测试main.lua解密"""
    print("=== 测试main.lua解密 ===")
    
    # 读取加密的main.lua
    lua_file = "gamedata/modcache/HYYM/lua/main.lua"
    if not os.path.exists(lua_file):
        print(f"文件不存在: {lua_file}")
        return
        
    decryptor = GameDecryptor()
    
    try:
        with open(lua_file, 'r', encoding='utf-8') as f:
            encrypted_content = f.read().strip()
            
        print(f"加密内容长度: {len(encrypted_content)}")
        print(f"加密内容开头: {encrypted_content[:100]}...")
        
        # 尝试解密
        decrypted = decryptor.extract_string(encrypted_content)
        
        if decrypted:
            print(f"解密成功! 解密内容长度: {len(decrypted)}")
            print(f"解密内容开头:\n{decrypted[:500]}...")
            
            # 保存解密结果
            output_file = "main_decrypted.lua"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(decrypted)
            print(f"解密结果已保存到: {output_file}")
        else:
            print("解密失败")
            
    except Exception as e:
        print(f"测试过程出错: {e}")

def test_directory_scan():
    """扫描目录中的加密文件"""
    print("\n=== 扫描加密文件 ===")
    
    directories = [
        "gamedata/modcache/HYYM/lua",
        "gamedata/modcache/HYYM/scripts", 
        "gamedata/saves"
    ]
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"\n扫描目录: {directory}")
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read(200)  # 只读前200字符
                            if content.startswith('@') or '@' in content:
                                print(f"  发现可能的加密文件: {file_path}")
                                print(f"    内容开头: {content[:50]}...")
                    except:
                        pass  # 忽略二进制文件等
        else:
            print(f"目录不存在: {directory}")

if __name__ == "__main__":
    test_main_lua()
    test_directory_scan()
