--[[

金庸群侠传X 技能逻辑扩展



]]--

local Tools = luanet.import_type('JyGame.Tools')

local Debug = luanet.import_type('UnityEngine.Debug')

local LuaTool = luanet.import_type('JyGame.LuaTool')

local CommonSettings = luanet.import_type('JyGame.CommonSettings')

local RuntimeData = luanet.import_type('JyGame.RuntimeData')

local Color = luanet.import_type('UnityEngine.Color')



--技能覆盖范围

function SKILL_getSize(skill ,sprite, size)

    if(skill.Name == "长歌飞虹剑" or skill.Name == "心剑.凤舞九天") then

    return 7

    end

    if(skill.Name == "蓬莱遗韵" or skill.Name == "圣技.太虚忘情" or skill.Name == "破势.凤舞九天" or skill.Name == "破阵.横扫千军") then

    return 6

    end

    if(skill.Name == "玉蜂针" or skill.Name == "玉蜂群" or skill.Name == "六脉神剑" or skill.Name == "大理绝学.六脉神剑奥义" or skill.Name == "冰魄银针" or skill.Name == "修罗血雨" or skill.Name == "子母铁胆" or skill.Name == "岳家枪法" or skill.Name == "倚天屠龙笔法" or skill.Name == "王技.纵横天下" or skill.Name == "王技.十步一杀" or skill.Name == "白虹掌力" or skill.Name == "玉璧神剑") then

    return 5

    end

    if(skill.Name == "五岳剑法" or skill.Name == "五岳镇苍穹" or skill.Name == "独孤九剑" or skill.Name == "无招胜有招" or skill.Name == "袖箭" or skill.Name == "天山六阳掌" or skill.Name == "极.生死符" or skill.Name == "阴风刀" or skill.Name == "幽灵剑法" or skill.Name == "幽剑.百合" or skill.Name == "奇门五转" or skill.Name == "泰山压顶") then

    return 4

    end

    if(skill.Name == "七弦无形剑" or skill.Name == "荒城之月" or skill.Name == "打狗棒法" or skill.Name == "天下无狗" or skill.Name == "棒打双犬" or skill.Name == "拨狗朝天" or skill.Name == "碧海潮生曲" or skill.Name == "火焰刀法" or skill.Name == "般若火舞" or skill.Name == "葵花迷影" or skill.Name == "地狱葵花" or skill.Name == "葵花缤舞" or skill.Name == "葵花针" or skill.Name == "天女散花" or skill.Name == "唐门绝技.暴雨梨花" or skill.Name == "生死符" or skill.Name == "冰狱寒岚" or skill.Name == "幻阴指" or skill.Name == "情人箭" or skill.Name == "太祖长拳" or skill.Name == "日月同辉" or skill.Name == "流云飞袖" or skill.Name == "五虎断门刀") then

    return 3

    end

    if(skill.Name == "三蜈五蟆烟" or skill.Name == "赤蝎粉" or skill.Name == "药王神掌" or skill.Name == "天问刀法" or skill.Name == "阴阳三合" or skill.Name == "庖丁解牛" or skill.Name == "大宗师" or skill.Name == "急风骤雨掌" or skill.Name == "金蛇游身掌" or skill.Name == "飘雪穿云掌" or skill.Name == "四象掌" or skill.Name == "反两仪刀法" or skill.Name == "朱砂掌" or skill.Name == "寒袖拂穴" or skill.Name == "碧云天.寒烟翠.相思雨") then

    return 2

    end

	return size

end



--技能施展范围

function SKILL_getCastSize(skill ,sprite, size)

    if(skill.Name == "弹指神通" or skill.Name == "弹指一瞬.碧海潮生" or skill.Name == "射雕神箭" or skill.Name == "流星神箭" or skill.Name == "天咏.鸿飞穿云箭") then

    return 7

    end

    if(skill.Name == "一阳指" or skill.Name == "天南一指" or skill.Name == "纯阳一指" or skill.Name == "先天一指") then

    return 6

    end

    if(skill.Name == "天魔手" or skill.Name == "参合指" or skill.Name == "黯然销魂掌" or skill.Name == "拖泥带水" or skill.Name == "呆若木鸡" or skill.Name == "黯然销魂.左手帝" or skill.Name == "摄魂魔音" or skill.Name == "奇门.点" or skill.Name == "绕指柔剑" or skill.Name == "五罗轻烟掌") then

    return 5

    end

    if(skill.Name == "般若掌" or skill.Name == "七星七绝剑" or skill.Name == "天山掌法" or skill.Name == "天罗地网掌" or skill.Name == "飘雪穿云掌") then

    return 4

    end

    if(skill.Name == "迎风一刀斩" or skill.Name == "云帚拂尘功" or skill.Name == "海叟钓法" or skill.Name == "九阴白骨爪" or skill.Name == "九阴魔爪" or skill.Name == "三蜈五蟆烟" or skill.Name == "赤蝎粉" or skill.Name == "五轮大转" or skill.Name == "十龙十象.飞轮海") then

    return 3

    end

    if(skill.Name == "冷月窥人" or skill.Name == "神刀斩" or skill.Name == "小楼一夜听春雨" or skill.Name == "落英神剑掌" or skill.Name == "东风绝技" or skill.Name == "蓬莱遗韵" or skill.Name == "七弦无形剑" or skill.Name == "碧海潮生曲" or skill.Name == "灵蛇杖法" or skill.Name == "流云飞袖" or skill.Name == "五虎断门刀" or skill.Name == "天山折梅手") then

    return 2

    end

    if(skill.Name == "游魂丝" or skill.Name == "情缠生死牵" or skill.Name == "伏魔杖法" or skill.Name == "白虹掌力" or skill.Name == "玉璧神剑" or skill.Name == "天山六阳掌" or skill.Name == "极.生死符" or skill.Name == "天问刀法" or skill.Name == "阴阳三合" or skill.Name == "庖丁解牛" or skill.Name == "大宗师" or skill.Name == "奇门五转" or skill.Name == "泰山压顶" or skill.Name == "天外飞仙" or skill.Name == "血心墨爪天魔掌" or skill.Name == "万妙无方.天魔乱舞" or skill.Name == "天心莲环" or skill.Name == "搜心剑法" or skill.Name == "一拍两散" or skill.Name == "寒袖拂穴" or skill.Name == "碧云天.寒烟翠.相思雨") then

    return 1

    end

    if(skill.Name == "药王神掌" or skill.Name == "金蛇游身掌" or skill.Name == "飘雪穿云掌" or skill.Name == "四象掌") then

    return 0

    end

	return size

end

--skill.Name技能名(绝技名跟奥义名是分开判定的)

--skill.Power技能威力

--skill.Suit技能适性

--skill.GetSkillTypeChinese()技能类型 外功 内功 绝技 特殊技能 奥义

--skill.Level技能等级

--skill.Type技能类型 0拳掌 1剑法 2刀法 3奇门





--技能消耗

function SKILL_getCostMp(skill,key ,role)

    if(skill.Name == "野球拳" or skill.Name == "野球拳.市井流氓" or skill.Name == "野球拳.胡搅蛮缠" or skill.Name == "男人见不得" or skill.Name == "魅舞飘带" or skill.Name == "普通攻击" or skill.Name == "奋力一击" or skill.Name == "内功攻击" or skill.Name == "犹豫一击")then

    return 0--

    end

    if(skill.Name == "岳家枪法" or skill.Name == "碧血丹心")then

    return 300--

    end

    if(skill.Name == "花拳绣腿" or skill.Name == "断子绝孙腿" or skill.Name == "袖箭" or skill.Name == "躺尸剑法" or skill.Name == "莲花掌" or skill.Name == "铁帚腿" or skill.Name == "罗汉拳" or skill.Name == "柴山十八路" or skill.Name == "疯魔杖法" or skill.Name == "恒山剑法" or skill.Name == "流星锤")then

    return skill.Level * skill.Power * 0.5

    end

    if(skill.Name == "毒龙鞭法" or skill.Name == "毒龙刀法" or skill.Name == "草原刀法" or skill.Name == "回打软鞭" or skill.Name == "雁行刀" or skill.Name == "雷震剑法" or skill.Name == "南山刀法" or skill.Name == "无量剑法" or skill.Name == "天龙剑法" or skill.Name == "春蚕掌法")then

    return skill.Power * 7

    end

    if(skill.Name == "松风剑法" or skill.Name == "万劫刀法" or skill.Name == "三分剑法" or skill.Name == "修罗刀法" or skill.Name == "姹女剑法" or skill.Name == "冷月剑法" or skill.Name == "岳家散手")then

    return skill.Power * 10

    end

    if(skill.Name == "百变千幻云雾十三式" or skill.Name == "峨眉剑法" or skill.Name == "黑沼灵狐" or skill.Name == "绕指柔剑" or skill.Name == "青莲掌法" or skill.Name == "绵掌" or skill.Name == "伏魔棍" or skill.Name == "狂风刀法" or skill.Name == "无影神针" or skill.Name == "雪山剑法" or skill.Name == "八卦游身掌" or skill.Name == "金蛇锥" or skill.Name == "蛇鹤八打" or skill.Name == "天山掌法" or skill.Name == "飘雪穿云掌" or skill.Name == "华山剑法" or skill.Name == "嵩山剑法" or skill.Name == "王技.纵横天下" or skill.Name == "王技.十步一杀" or skill.Name == "倚天屠龙笔法" or skill.Name == "流云飞袖")then

    return skill.Power * 14

    end

    if(skill.Name == "辟邪剑法" or skill.Name == "鬼易辟邪" or skill.Name == "葵花迷影" or skill.Name == "地狱葵花" or skill.Name == "葵花缤舞" or skill.Name == "葵花针" or skill.Name == "破势.凤舞九天" or skill.Name == "破阵.横扫千军" or skill.Name == "九阴白骨爪" or skill.Name == "九阴魔爪" or skill.Name == "赤蝎粉" or skill.Name == "龙爪擒拿手" or skill.Name == "三蜈五蟆烟" or skill.Name == "子母铁胆" or skill.Name == "连环迷踪腿" or skill.Name == "日月同辉" or skill.Name == "漫天花雨" or skill.Name == "日月鞭法" or skill.Name == "海叟钓法" or skill.Name == "药王神掌" or skill.Name == "落英神剑" or skill.Name == "万紫千红")then

    return skill.Level * skill.Power * 0.8

    end

    if(skill.Name == "打狗棒法" or skill.Name == "天下无狗" or skill.Name == "棒打双犬" or skill.Name == "拨狗朝天" or skill.Name == "独孤九剑" or skill.Name == "无招胜有招" or skill.Name == "美女拳法" or skill.Name == "夫妻刀法" or skill.Name == "鸳鸯刀法" or skill.Name == "归藏剑" or skill.Name == "无影神拳" or skill.Name == "游魂丝" or skill.Name == "情缠生死牵" or skill.Name == "幽灵剑法" or skill.Name == "幽剑.百合" or skill.Name == "苗家剑法" or skill.Name == "胡家刀法" or skill.Name == "遥看电跃龙为马" or skill.Name == "且向花间留晚照" or skill.Name == "玉箫剑法" or skill.Name == "金声玉振" or skill.Name == "凤曲长鸣")then

    return skill.Level * skill.Power * 1

    end

    if(skill.Name == "射雕神箭" or skill.Name == "流星神箭" or skill.Name == "天咏.鸿飞穿云箭" or skill.Name == "柔云剑法" or skill.Name == "连城剑法" or skill.Name == "真.唐诗剑法" or skill.Name == "孔雀开屏" or skill.Name == "唐诗剑法" or skill.Name == "迎风一刀斩" or skill.Name == "玉蜂针" or skill.Name == "玉蜂群" or skill.Name == "白虹掌力" or skill.Name == "玉璧神剑")then

    return skill.Power * 16*(skill.Size ^ 0.2)

    end

    if(skill.Name == "五岳剑法" or skill.Name == "五岳镇苍穹" or skill.Name == "蓬莱遗韵" or skill.Name == "圣技.太虚忘情" or skill.Name == "太白剑法" or skill.Name == "神门十三剑" or skill.Name == "玉女剑法" or skill.Name == "全真剑法" or skill.Name == "天罗地网掌" or skill.Name == "梅花拳" or skill.Name == "天女散花" or skill.Name == "唐门绝技.暴雨梨花")then

    return skill.Power * 16*(skill.Size ^ 0.3)

    end

    if(skill.Name == "三无三不手" or skill.Name == "冰魄银针" or skill.Name == "修罗血雨" or skill.Name == "玉女素心剑" or skill.Name == "古墓绝学.双剑合璧" or skill.Name == "葵花绝学.天罗剑岚" or skill.Name == "天山剑法")then

    return skill.Power * 16*(skill.Size ^ 0.5)

    end

    if(skill.Name == "三阴蜈蚣爪" or skill.Name == "五毒神掌" or skill.Name == "天魔手" or skill.Name == "搜心剑法" or skill.Name == "太祖长拳" or skill.Name == "勾魂指" or skill.Name == "寒袖拂穴" or skill.Name == "碧云天.寒烟翠.相思雨" or skill.Name == "天外飞仙")then

    return skill.Power * 16*(skill.Size ^ 0.75)

    end

    if(skill.Name == "情人箭" or skill.Name == "天竺佛指" or skill.Name == "碧海潮生曲" or skill.Name == "天山六阳掌" or skill.Name == "极.生死符" or skill.Name == "七弦无形剑" or skill.Name == "空明拳" or skill.Name == "空明神拳" or skill.Name == "大伏魔拳" or skill.Name == "阴风刀" or skill.Name == "大金刚掌" or skill.Name == "火焰刀法" or skill.Name == "般若火舞" or skill.Name == "玄铁剑法")then

    return skill.Power * 16*(skill.Size ^ 0.9)

    end

    if(skill.Name == "弹指神通" or skill.Name == "弹指一瞬.碧海潮生" or skill.Name == "落英神剑掌" or skill.Name == "东风绝技" or skill.Name == "荒城之月" or skill.Name == "天山折梅手" or skill.Name == "灵鹫宫绝学.天地唯我独尊" or skill.Name == "斗转星移" or skill.Name == "天问刀法" or skill.Name == "阴阳三合" or skill.Name == "生死符" or skill.Name == "冰狱寒岚" or skill.Name == "袖里乾坤" or skill.Name == "五轮大转" or skill.Name == "十龙十象.飞轮海")then

    return skill.Power * 16*(skill.Size ^ 1.05)

    end

    if(skill.Name == "黯然销魂掌" or skill.Name == "拖泥带水" or skill.Name == "呆若木鸡" or skill.Name == "黯然销魂.左手帝" or skill.Name == "神驼雪山掌" or skill.Name == "一阳指" or skill.Name == "天南一指" or skill.Name == "纯阳一指" or skill.Name == "先天一指" or skill.Name == "玄冥神掌" or skill.Name == "十步一杀" or skill.Name == "摄魂魔音" or skill.Name == "铁掌" or skill.Name == "奇门五转" or skill.Name == "泰山压顶" or skill.Name == "血心墨爪天魔掌" or skill.Name == "万妙无方.天魔乱舞")then

    return skill.Power * 16*(skill.Size ^ 1.2)

    end

    if(skill.Name == "降龙十八掌" or skill.Name == "如来千手法" or skill.Name == "千手观音" or skill.Name == "无相如来" or skill.Name == "大搜魂针" or skill.Name == "天心莲环" or skill.Name == "参合指")then

    return skill.Power * 16*(skill.Size ^ 1.3)

    end

    if(skill.Name == "幻阴指" or skill.Name == "伏魔杖法")then

    return skill.Power * 16*(skill.Size ^ 1.55)

    end

    if(skill.Name == "天魔刀" or skill.Name == "神刀斩")then

    return skill.Power * 16*(skill.Size ^ 1.7)

    end

    if(skill.Name == "东皇封印术" or skill.Name == "神技.七光御阵" or skill.Name == "不死七幻")then

    return skill.Power * 16*(skill.Size ^ 2.2)

    end

    if(skill.Name == "阴阳倒乱刃")then

    return skill.Power * 16*(skill.Size ^ 2.5)

    end



if(role ~= nil)then

    if(role:HasTalent("降龙神掌") and (skill.Name == "降龙十八掌" or skill.Name == "降龙十八掌.飞龙在天" or skill.Name == "降龙十八掌.时乘六龙"))then

    return 1000--如果角色拥有天赋降龙神掌，同时技能名是降龙十八掌或者降龙十八掌绝技飞龙在天或者降龙十八掌绝技时乘六龙，则技能消耗为1000

    end

end

	return skill.Power * 16*(skill.Size ^ 0.6)--不满足上面的条件则返回默认消耗，技能威力*16*技能范围的平方根(降低了技能范围对技能消耗的影响)

end





function SKILL_getSuit(skill,key ,role ,suit)

if(role ~= nil)then

    if(role:HasTalent("女性") and (skill.Name == "花拳绣腿" or skill.Name == "断子绝孙腿"))then

    suit = suit - 0.1--如果角色拥有女性天赋，那么花拳绣腿与断子绝孙腿的适性降低10%(阴适性降低是提升，阳适性则反之，该属性对阴阳调和类武功无效)

    end

end

	return suit--请勿修改

end





function SKILL_getPower(skill,key ,role ,power)

if(role ~= nil)then

    if(role:HasTalent("六脉精义") and (skill.Name == "六脉神剑" or skill.Name == "大理绝学.六脉神剑奥义"))then

    power = power * 1.2--如果角色拥有六脉精义天赋，那么六脉神剑威力提升20%(如果需要六脉神剑系列的所有武功都生效这次改动，需要把六脉神剑底下的所有技能名都添加上去)

    end

    if(role:HasTalent("鸳鸯刀") and (skill.Name == "鸳鸯刀法" or skill.Name == "夫妻刀法" or skill.Name == "百花错拳" or skill.Name == "连环迷踪腿" or skill.Name == "子母铁胆" or skill.Name == "金屋藏娇" or skill.Name == "弄玉吹箫"))then

    power = power * 1.2

    end

    if(role:HasTalent("明镜非台") and (skill.Name == "全真剑法" or skill.Name == "全真绝学.重阳剑法"))then

    power = power * 1.5

    end

    if(role:HasTalent("峨眉宗师") and (skill.Name == "九阴白骨爪" or skill.Name == "摧心掌" or skill.Name == "九阴魔爪"))then

    power = power * 1.5

    end

end

	return power--请勿修改

end

function SKILL_getCoverType(skill,key ,role ,covertype)

if (skill.Name == "奇门.点" or skill.Name == "迎风一刀斩" or skill.Name == "子母铁胆" or skill.Name == "流星神箭" or skill.Name == "射雕神箭" or skill.Name == "天咏.鸿飞穿云箭" or skill.Name == "绕指柔剑") then

    return 0

 end 

 if (skill.Name == "血刀大法" or skill.Name == "摧坚神爪" or skill.Name == "九阴神爪" or skill.Name == "大伏魔拳" or skill.Name == "灵蛇拳" or skill.Name == "千噬万毒" or skill.Name == "夫妻刀法" or skill.Name == "五岳剑法" or skill.Name == "五岳镇苍穹" or skill.Name == "如来千手法" or skill.Name == "千手观音" or skill.Name == "无相如来" or skill.Name == "两仪剑法" or skill.Name == "反两仪刀法" or skill.Name == "日月同辉" or skill.Name == "奇门五转" or skill.Name == "泰山压顶" or skill.Name == "血心墨爪天魔掌" or skill.Name == "万妙无方.天魔乱舞") then

    return 1

 end 

 if (skill.Name == "天山折梅手" or skill.Name == "灵鹫宫绝学.天地唯我独尊" or skill.Name == "金蛇锥" or skill.Name == "玄冥神掌" or skill.Name == "十步一杀" or skill.Name == "打狗棒法" or skill.Name == "天下无狗" or skill.Name == "棒打双犬" or skill.Name == "拨狗朝天" or skill.Name == "火焰刀法" or skill.Name == "般若火舞" or skill.Name == "剑法.刺" or skill.Name == "大搜魂针" or skill.Name == "上天入地.瞬狱一杀" or skill.Name == "乱披风剑势" or skill.Name == "天女散花" or skill.Name == "唐门绝技.暴雨梨花" or skill.Name == "寒袖拂穴" or skill.Name == "碧云天.寒烟翠.相思雨") then

    return 2

 end 

 if (skill.Name == "岳家枪法" or skill.Name == "天山六阳掌" or skill.Name == "无影神针" or skill.Name == "情人箭" or skill.Name == "袖箭" or skill.Name == "玉蜂针" or skill.Name == "玉蜂群" or skill.Name == "六脉神剑" or skill.Name == "大理绝学.六脉神剑奥义" or skill.Name == "白虹掌力" or skill.Name == "玉璧神剑" or skill.Name == "阴风刀" or skill.Name == "药王神掌" or skill.Name == "急风骤雨掌" or skill.Name == "幻阴指" or skill.Name == "太祖长拳" or skill.Name == "点墨山河" or skill.Name == "朱砂掌" or skill.Name == "五虎断门刀" or skill.Name == "长江三叠浪" or skill.Name == "长江三叠浪.滔滔不绝") then

    return 3

 end 

 if (skill.Name == "碧海潮生曲" or skill.Name == "荒城之月" or skill.Name == "摄魂魔音" or skill.Name == "绝云负天" or skill.Name == "一梦红颜" or skill.Name == "蓬莱遗韵" or skill.Name == "圣技.太虚忘情" or skill.Name == "空明拳" or skill.Name == "空明神拳" or skill.Name == "游魂丝" or skill.Name == "情缠生死牵" or skill.Name == "赤蝎粉" or skill.Name == "三蜈五蟆烟" or skill.Name == "七弦无形剑" or skill.Name == "三无三不手" or skill.Name == "袖里乾坤" or skill.Name == "庖丁解牛" or skill.Name == "寒江独钓" or skill.Name == "大宗师" or skill.Name == "金蛇游身掌" or skill.Name == "飘雪穿云掌" or skill.Name == "四象掌" or skill.Name == "王技.纵横天下" or skill.Name == "王技.十步一杀" or skill.Name == "神技.七光御阵" or skill.Name == "流云飞袖") then

    return 4

 end 

 if (skill.Name == "黑血神针" or skill.Name == "天地孤影.日月凌空" or skill.Name == "唳血万毒" or skill.Name == "葵花迷影" or skill.Name == "地狱葵花" or skill.Name == "葵花缤舞" or skill.Name == "葵花针" or skill.Name == "破势.凤舞九天" or skill.Name == "破阵.横扫千军" or skill.Name == "冰魄银针" or skill.Name == "修罗血雨" or skill.Name == "玄铁剑法" or skill.Name == "玉女素心剑" or skill.Name == "葵花绝学.天罗剑岚" or skill.Name == "古墓绝学.双剑合璧" or skill.Name == "降龙十八掌" or skill.Name == "降龙十八掌.履霜冰至" or skill.Name == "降龙十八掌.震惊百里" or skill.Name == "降龙十八掌.密云不雨" or skill.Name == "降龙十八掌.突如其来" or skill.Name == "降龙十八掌.时乘六龙" or skill.Name == "一剑西来" or skill.Name == "刀法.劈" or skill.Name == "漫天花雨" or skill.Name == "生死符" or skill.Name == "冰狱寒岚" or skill.Name == "花拳绣腿" or skill.Name == "倚天屠龙笔法") then

    return 5

 end

 if (skill.Name == "连城剑法" or skill.Name == "真.唐诗剑法" or skill.Name == "孔雀开屏" or skill.Name == "唐诗剑法" or skill.Name == "落英神剑掌" or skill.Name == "东风绝技" or skill.Name == "拈花指" or skill.Name == "斗转星移" or skill.Name == "全真剑法" or skill.Name == "无影神拳") then

    return 6

 end 

 if (skill.Name == "千蛛万毒手" or skill.Name == "鬼戾天幕" or skill.Name == "金蛇剑法" or skill.Name == "七星七绝剑" or skill.Name == "毒龙刀法" or skill.Name == "拂尘") then

    return 7

 end 

 if (skill.Name == "铁掌" or skill.Name == "绝户虎爪手") then

    return 8

 end 

 if skill.Level >= 101 and skill.Name == "男人见不得" then

    return 2

 end 

 if skill.Level >= 101 and skill.Name == "全真剑法" then

    return 2

 end 

if(role ~= nil)then

 if role:HasTalent("女性") and skill.Name == "美女拳法" then

    return 2--满足条件直接return返回覆盖类型 0点 1十字 2米 3 直线 4 面 5扇 6 环 7X 8身前点

 end

end

	return covertype--请勿修改

end





function InternalSkill_getYin(role, yin)

if(role ~= nil)then

    if role:HasTalent("九阴极意") then

    yin = yin * 1.3--如果角色拥有天赋九阴极意，那么内功的阴适性提升30%

    end

    if role:HasTalent("芳兰竟体") then

    yin = yin * 1.2

    end

end

	return yin--请勿修改

end

function InternalSkill_getYang(role, yang)

if(role ~= nil)then

    if role:HasTalent("九阳无极") then

    yang = yang * 1.3--如果角色拥有天赋九阳无极，那么内功的阳适性提升30%

    end

    if role:HasTalent("玉兔当阳") then

    yang = yang * 1.2

    end

end

	return yang--请勿修改

end





function InternalSkill_getAttack(role, skill, attack)

if(role ~= nil)then

    if role:HasTalent("易筋洗髓") and skill.Name == "易筋经" then

    attack = attack + 0.4--如果角色拥有天赋易筋神功，同时内功名为易筋经那么内功的攻击增加40%

    end

    if role:HasTalent("星宿小仙") and skill.Name == "化功大法" then

    attack = attack * 1.4

    end

    if role:HasTalent("星宿小仙") and skill.Name == "星宿心法" then

    attack = attack * 1.4

    end

    if role:HasTalent("阉人") and skill.Name == "紫霞神功" then

    attack = attack * 1.4

    end

end

	return attack

end

function InternalSkill_getDefence(role, skill ,defence)

if(role ~= nil)then

    if role:HasTalent("星宿小仙") and skill.Name == "化功大法" then

    defence = defence * 1.4

    end

    if role:HasTalent("星宿小仙") and skill.Name == "星宿心法" then

    defence = defence * 1.4

    end

end

	return defence

end