@echo off
chcp 65001 >nul
echo ========================================
echo 游戏文件加密解密工具 - 依赖安装脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python环境
    echo 请先安装Python 3.6或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 正在安装必要的依赖库...
echo.

echo 安装 pycryptodome...
pip install pycryptodome
if errorlevel 1 (
    echo ❌ pycryptodome 安装失败
    echo 尝试使用国内镜像源...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple pycryptodome
)

echo.
echo 检查安装结果...
python -c "import Crypto; print('✅ pycryptodome 安装成功')" 2>nul
if errorlevel 1 (
    echo ❌ pycryptodome 安装验证失败
    echo 请手动运行：pip install pycryptodome
    pause
    exit /b 1
)

echo.
echo 🎉 所有依赖安装完成！
echo.
echo 现在你可以：
echo 1. 双击 "启动工具.bat" 启动图形界面
echo 2. 或者运行 "python game_crypto_tool.py"
echo.
echo 使用说明请查看：
echo - 快速开始.md
echo - 使用说明.md
echo.
pause
