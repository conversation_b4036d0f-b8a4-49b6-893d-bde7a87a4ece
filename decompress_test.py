#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试解压缩逻辑
"""

def decompress_string(encoded_str):
    """
    实现C#代码中的解压缩逻辑:
    for (int i = 0; i < str.Length; i += 2)
    {
        int num = (int)(str[i + 1] - '0');
        for (int j = 0; j < num; j++)
        {
            stringBuilder.Append(str[i]);
        }
    }
    """
    result = []
    i = 0
    
    print(f"开始解压缩，字符串长度: {len(encoded_str)}")
    
    while i < len(encoded_str) - 1:
        char = encoded_str[i]
        count_char = encoded_str[i + 1]
        count = ord(count_char) - ord('0')
        
        if i < 20:  # 只显示前几个用于调试
            print(f"位置{i}: 字符='{char}' (ord={ord(char)}), 计数字符='{count_char}' (ord={ord(count_char)}), 计数={count}")
        
        if 0 <= count <= 9:
            result.append(char * count)
            if i < 20:
                print(f"  重复{count}次: {repr(char * count)}")
        else:
            # 如果不是有效数字，可能需要其他处理
            if i < 20:
                print(f"  无效计数{count}，跳过")
        
        i += 2
        
        if i > 40:  # 只处理前几个用于测试
            print("  ...")
            break
    
    return ''.join(result)

def test_decompress():
    # 读取之前解码的数据
    with open("skill_method2_latin-1.txt", 'r', encoding='latin-1') as f:
        encoded_data = f.read()
    
    print(f"输入数据长度: {len(encoded_data)}")
    print(f"前50字符: {repr(encoded_data[:50])}")
    
    # 尝试解压缩
    decompressed = decompress_string(encoded_data)
    
    print(f"\n解压缩结果长度: {len(decompressed)}")
    print(f"前100字符: {repr(decompressed[:100])}")
    
    # 保存结果
    with open("skill_decompressed.txt", 'w', encoding='utf-8') as f:
        f.write(decompressed)
    
    print("结果保存到: skill_decompressed.txt")
    
    # 检查是否包含Lua代码特征
    if 'function' in decompressed or 'local' in decompressed or 'end' in decompressed:
        print("✓ 发现Lua代码特征！")
    else:
        print("✗ 未发现明显的Lua代码特征")

if __name__ == "__main__":
    test_decompress()
