#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整理工具包，创建一个干净的发布版本
"""

import os
import shutil

def create_tool_package():
    """创建工具包"""
    
    # 工具包目录
    package_dir = "游戏加密解密工具包"
    
    # 如果目录存在，先删除
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    
    # 创建目录
    os.makedirs(package_dir)
    
    # 需要包含的文件
    files_to_include = [
        "game_crypto_tool.py",      # 主程序
        "启动工具.bat",             # 启动脚本
        "安装依赖.bat",             # 安装脚本
        "README.md",                # 项目说明
        "快速开始.md",              # 快速指南
        "使用说明.md",              # 详细说明
        "test_encrypt.py",          # 测试脚本
    ]
    
    print("正在创建工具包...")
    
    # 复制文件
    for file_name in files_to_include:
        if os.path.exists(file_name):
            shutil.copy2(file_name, package_dir)
            print(f"✅ 已复制: {file_name}")
        else:
            print(f"❌ 文件不存在: {file_name}")
    
    # 创建示例目录结构
    example_dir = os.path.join(package_dir, "示例目录结构")
    os.makedirs(example_dir)
    
    # 创建子目录
    subdirs = [
        "原始文件备份",
        "解密后的文件", 
        "修改后的文件",
        "重新加密的文件"
    ]
    
    for subdir in subdirs:
        os.makedirs(os.path.join(example_dir, subdir))
        # 创建说明文件
        with open(os.path.join(example_dir, subdir, "说明.txt"), 'w', encoding='utf-8') as f:
            if subdir == "原始文件备份":
                f.write("这里存放游戏原始的加密文件备份\n用于恢复和对比")
            elif subdir == "解密后的文件":
                f.write("这里存放解密后的明文文件\n可以直接编辑修改")
            elif subdir == "修改后的文件":
                f.write("这里存放你修改过的文件\n准备重新加密")
            elif subdir == "重新加密的文件":
                f.write("这里存放重新加密后的文件\n用于替换游戏目录中的文件")
    
    print(f"✅ 已创建示例目录结构: {example_dir}")
    
    # 创建版本信息文件
    version_file = os.path.join(package_dir, "版本信息.txt")
    with open(version_file, 'w', encoding='utf-8') as f:
        f.write("""游戏文件加密解密工具 v1.0.0

发布日期: 2024年
适用游戏: 金庸群侠传X

功能特性:
✅ 支持Lua脚本文件解密/加密
✅ 支持XML配置文件解密/加密  
✅ 图形化界面，操作简单
✅ 批量处理整个目录
✅ 详细的操作日志
✅ 完整的错误处理

技术规格:
- 加密算法: 3DES ECB模式
- 密钥来源: "Yh$45Ct@mods" 的MD5哈希
- 编码方式: Base64
- 支持的Python版本: 3.6+

使用方法:
1. 运行 "安装依赖.bat" 安装必要库
2. 双击 "启动工具.bat" 启动程序
3. 参考 "快速开始.md" 进行操作

注意事项:
- 使用前请备份原始文件
- 建议在测试环境中验证修改
- 不要直接在游戏目录中操作

更多信息请查看 README.md 和使用说明.md
""")
    
    print(f"✅ 已创建版本信息: {version_file}")
    
    # 统计文件
    total_files = 0
    total_size = 0
    
    for root, dirs, files in os.walk(package_dir):
        for file in files:
            file_path = os.path.join(root, file)
            total_files += 1
            total_size += os.path.getsize(file_path)
    
    print(f"\n🎉 工具包创建完成！")
    print(f"📁 目录: {package_dir}")
    print(f"📄 文件数量: {total_files}")
    print(f"💾 总大小: {total_size / 1024:.1f} KB")
    
    print(f"\n📋 工具包内容:")
    for root, dirs, files in os.walk(package_dir):
        level = root.replace(package_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{subindent}{file}")

if __name__ == "__main__":
    create_tool_package()
