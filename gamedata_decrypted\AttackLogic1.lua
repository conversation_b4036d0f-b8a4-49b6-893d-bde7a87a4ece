--[[

金庸群侠传X

Mod:红颜一梦

    By:宿玉

攻击逻辑扩展

]]--



local Tools = luanet.import_type('JyGame.Tools')

local Debug = luanet.import_type('UnityEngine.Debug')

local LuaTool = luanet.import_type('JyGame.LuaTool')

local CommonSettings = luanet.import_type('JyGame.CommonSettings')

local RuntimeData = luanet.import_type('JyGame.RuntimeData')

local BattleStatus = luanet.import_type('JyGame.BattleStatus')

local  Item= luanet.import_type('JyGame.Item')

--扩展特殊攻击

function AttackLogic_extendSpecialSkill(result, skill, sourceSprite, targetSprite, bf)

	if(skill.Name == "扳手腕") then

		--施展技能弹的对话

		result:AddCastInfo(sourceSprite, LuaTool.MakeStringArray({"喝啊！","比比谁的力气大！！"}))

		

		--造成攻击者100+ 0~50*臂力差 的伤害

		local deltaBili = math.abs(sourceSprite.Role.AttributesFinal["bili"], targetSprite.Role.AttributesFinal["bili"])

		result.Hp = 100 + Tools.GetRandomInt(0, 50*deltaBili)

return result

		end

	

	--特技【爱的鞭笞】 将行动值交给队友

	--可以让队友出手多次的辅助技能

	if(skill.Name == "爱的鞭笞" and targetSprite.Team == sourceSprite.Team) then

		targetSprite.Sp=targetSprite.Sp+100

		bf:Log(sourceSprite.Role.Name .. "使用【爱的鞭笞】，" .. targetSprite.Role.Name .. "获得额外行动!") 

return result	

	end	



if(skill.Name == "凯旋") then	

		if (sourceSprite.Team == 1) then

			bf.Status = BattleStatus.Win

		end

		return result

	end



if(skill.Name == "无中生无") then

result:AddCastInfo(sourceSprite, LuaTool.MakeStringArray({"看看是啥","你有我也有"}))

return result

end



	--请勿修改

	return result

end



--扩展天赋（包含在AI计算内）

--不要在此函数中直接扣除血量、或者调用sprite的讲话函数，此函数所有的操作应该针对(AttackResult)result变量进行

function AttackLogic_extendTalents(sourceSprite, targetSprite, skill, bf, result)



	--天赋：大力士，每次攻击附加臂力的50%的伤害

	if(sourceSprite.Role:HasTalent("大力士")) then

		result.Hp = result.Hp + tonumber(sourceSprite.Role.AttributesFinal["bili"] * 0.5)

		result:AddCastInfo(sourceSprite, LuaTool.MakeStringArray({"受死吧，小不点！","大力士就是我！"}))

	end

	--天赋：掠影，剑法招式附加2倍的剑法技巧伤害

	if(sourceSprite.Role:HasTalent("掠影") and skill.Type == 1) then

		if (targetSprite.Team ~= sourceSprite.Team ) then 

			result.Hp = result.Hp + math.floor(sourceSprite.Role.AttributesFinal["jianfa"] * 2) 

			result:AddCastInfo(sourceSprite, LuaTool.MakeStringArray({"…………","我即是幻影……"}), 1)	

		end

	end	

	--天赋：暗香，奇门招式附加2倍的奇门技巧伤害

	if(sourceSprite.Role:HasTalent("暗香") and skill.Type == 3) then

		if (targetSprite.Team ~= sourceSprite.Team ) then 

			result.Hp = result.Hp + math.floor(sourceSprite.Role.AttributesFinal["qimen"] * 2) 

			result:AddCastInfo(sourceSprite, LuaTool.MakeStringArray({"…………","你无处可逃！"}), 1)	

		end

	end

	--天赋：天罡，拳掌招式附加2倍的拳掌技巧伤害	

	if(sourceSprite.Role:HasTalent("天罡") and skill.Type == 0) then

		if (targetSprite.Team ~= sourceSprite.Team ) then 

			result.Hp = result.Hp + math.floor(sourceSprite.Role.AttributesFinal["quanzhang"] * 2) 

			result:AddCastInfo(sourceSprite, LuaTool.MakeStringArray({"…………","不动如山"}), 1)	

		end

	end

	--天赋：寒光，刀法招式附加2倍的刀法技巧伤害	

	if(sourceSprite.Role:HasTalent("寒光") and skill.Type == 2) then

		if (targetSprite.Team ~= sourceSprite.Team ) then 

			result.Hp = result.Hp + math.floor(sourceSprite.Role.AttributesFinal["daofa"] * 2) 

			result:AddCastInfo(sourceSprite, LuaTool.MakeStringArray({"…………","斩！"}), 1)	

		end

	end

	--天赋：百世经纶，随等级提高攻击的伤害	

	if(sourceSprite.Role:HasTalent("百世经纶")) then

		if (targetSprite.Team ~= sourceSprite.Team ) then 

			result.Hp = result.Hp + math.floor(sourceSprite.Role.Level * 10)

			result:AddCastInfo(sourceSprite, LuaTool.MakeStringArray({"你还是太年轻了"}), 1)	

		end

	end	

	--天赋  绝对性别优势:男神  女神

	if(targetSprite.Role:HasTalent("绝世美男") and targetSprite.Team ~= sourceSprite.Team) then 

		--触发条件 有天赋 状态 战场对敌防御

		if (sourceSprite.Role.AttributesFinal["female"] == 1 and targetSprite.Role.AttributesFinal["female"] == 0) then

			--判断敌我双方都满足触发的性别条件

			--几率执行高度减伤 

			if (Tools.ProbabilityTest(0.3)) then

       			result.Hp = result.Hp * 0.5

				result:AddCastInfo(sourceSprite, LuaTool.MakeStringArray({"他长得好帅呀，还是打他轻一点吧", "他好帅，我都没力气了……"}), 0.75)

  			end

  		end

  	end



	if(targetSprite.Role:HasTalent("绝世美女") and targetSprite.Team ~= sourceSprite.Team) then

		if(sourceSprite.Role.AttributesFinal["female"] == 0 and targetSprite.Role.AttributesFinal["female"] == 1) then

			if(Tools.ProbabilityTest(0.3)) then

				result.Hp = result.Hp * 0.5

				result:AddCastInfo(sourceSprite, LuaTool.MakeStringArray({"哎，好美丽的女子", "不能伤到她……"}), 0.75)

			end

		end

	end

	--与原版的好色相对应的天赋

	if (targetSprite.Role:HasTalent("妓女") and targetSprite.Team ~= sourceSprite.Team) then

		if (sourceSprite.Role.AttributesFinal["female"] == 0 and targetSprite.Role.AttributesFinal["female"] == 1) then

			if (sourceSprite.Role:HasTalent("好色")) then

				result.Hp = result.Hp * 2.0

				result:AddCastInfo(targetSprite, LuaTool.MakeStringArray({"来吧，互相伤害吧", "爱的深，伤得痛！"}), 0.75)

			else

				result.Hp = result.Hp * 1.25

				result:AddCastInfo(targetSprite, LuaTool.MakeStringArray({"不会怜香惜玉么？", "公子下手未免重了"}), 0.75)

			end

		end

	end

	if(sourceSprite.Role:HasTalent("妓女") and targetSprite.Team ~= sourceSprite.Team) then

		if (sourceSprite.Role.AttributesFinal["female"] == 1 and targetSprite.Role.AttributesFinal["female"] == 0) then

			if (targetSprite.Role:HasTalent("好色")) then

				result.Hp = result.Hp * 1.5

				result:AddCastInfo(sourceSprite, LuaTool.MakeStringArray({"来吧，互相伤害吧", "爱的深，伤得痛！"}), 0.75)

			else

				result.Hp = result.Hp * 1.25

				result:AddCastInfo(sourceSprite, LuaTool.MakeStringArray({"最喜欢欺负小帅哥了", "小鲜肉，入碗！"}), 0.75)

			end

		end

	end



--[[CP天赋模板

if(sourceSprite.Role:HasTalent("夫妻同心") then

local sourceSay1={"梅雪合璧","春梅绽放"}

	for _,sprite in pairs(bf.SpritesTable) do

 if sprite.Team == sourceSprite.Team and sprite.Role:HasTalent("夫妻同心") then

result.Hp = result.Hp*1.4

--result:ClearInfo(sourceSprite)

sprite.Skills[math.random(table.getn(sprite.Skills))]

battlefield:Attack(sprite, sprite.Skills[0], targetSprite.X, targetSprite.Y)

bf:Log(sourceSprite.Role.Name .."和【"..sprite.Role.Name.."】合击天赋发动！伤害增加！")

if result.critical == false then

if (Tools.ProbabilityTest(0.2)) then

result.critical =true

bf:Log(sourceSprite.Role.Name .. "合璧天赋生效！伤害暴击！")

end

end

end

break

end

end]]



end





--扩展天赋2（不包含在AI计算内）

--与扩展天赋区别是在此的修改是立即结算

function AttackLogic_extendTalents2(sourceSprite, targetSprite, skill, bf, result)

	

	--天赋：破碎虚空，20%概率直接将对手内力打空

	if(sourceSprite.Role:HasTalent("破碎虚空")) then

		if(Tools.ProbabilityTest(0.2)) then

			targetSprite.Mp = 0

			bf:Log(sourceSprite.Role.Name .. "天赋【破碎虚空】发动！直接打空" .. targetSprite.Role.Name .. "的内力!") --记录日志

			sourceSprite:Say("吾已破碎虚空，不要挣扎了，快快领死吧。")

		end

	end



--CP天赋的正确放置

if(sourceSprite.Role:HasTalent("异世人") and sourceSprite.Role.Key == "主角" and skill.Type==0) then

local sourceSay1={"梅雪合璧","春梅绽放"}

	for _,sprite in pairs(bf.SpritesTable) do

 if sprite.Team == sourceSprite.Team and sprite.Role:HasTalent("武学奇才") and sprite.Role.Key == "女主" then

result.Hp = result.Hp*1.4

--result:ClearInfo(sourceSprite)

result:AddCastInfo(sourceSprite, sourceSay1[math.random(table.getn(sourceSay1))], 1)

bf:Attack(sprite,skill,targetSprite.X,targetSprite.Y)

bf:ShowSkillAnimation(skill,targetSprite.X,targetSprite.Y,callback)

bf:Log("【"..sourceSprite.Role.Name .."】和【"..sprite.Role.Name.."】合击天赋发动！伤害增加！")

if result.critical == false then

if (Tools.ProbabilityTest(0.2)) then

result.critical =true

bf:Log(sourceSprite.Role.Name .. "合璧天赋生效！伤害暴击！")

end

end

end

--break

end

end



if(sourceSprite.Role:HasTalent("百合花开") and sourceSprite.Role.Key == "主角" and skill.Type==0) then

local sourceSay1={"梅雪合璧","春梅绽放"}

	for _,sprite in pairs(bf.SpritesTable) do

 if sprite.Team == sourceSprite.Team and sprite.Role:HasTalent("百合花开") then

result.Hp = result.Hp*1.4

--result:ClearInfo(sourceSprite)

result:AddCastInfo(sourceSprite, sourceSay1[math.random(table.getn(sourceSay1))], 1)

bf:Attack(sprite,skill,targetSprite.X,targetSprite.Y)

bf:ShowSkillAnimation(skill,targetSprite.X,targetSprite.Y,callback)

bf:Log("【"..sourceSprite.Role.Name .."】和【"..sprite.Role.Name.."】合击天赋发动！伤害增加！")

if result.critical == false then

if (Tools.ProbabilityTest(0.2)) then

result.critical =true

bf:Log(sourceSprite.Role.Name .. "合璧天赋生效！伤害暴击！")

end

end

end

--break

end

end



if(sourceSprite.Role:HasTalent("相见欢") and sourceSprite.Role.Key == "胡斐" and skill.Type==0) then

local sourceSay1={"借如生死别","安得长苦悲"}

	for _,sprite in pairs(bf.SpritesTable) do

 if sprite.Team == sourceSprite.Team and sprite.Role:HasTalent("相见欢") and sprite.Role.Key == "袁紫衣" then

result.Hp = result.Hp*1.3

--result:ClearInfo(sourceSprite)

result:AddCastInfo(sourceSprite, sourceSay1[math.random(table.getn(sourceSay1))], 1)

bf:Attack(sprite,skill,targetSprite.X,targetSprite.Y)

bf:ShowSkillAnimation(skill,targetSprite.X,targetSprite.Y,callback)

bf:Log("【"..sourceSprite.Role.Name .."】和【"..sprite.Role.Name.."】情侣天赋发动！伤害增加！")

if result.critical == false then

if (Tools.ProbabilityTest(0.3)) then

result.critical =true

bf:Log(sourceSprite.Role.Name .. "情侣天赋生效！伤害暴击！")

end

end

end

--break

end

end



if(sourceSprite.Role:HasTalent("心有灵犀") and sourceSprite.Role.Key == "岳灵珊" and skill.Type==1) then

local sourceSay1={"我们一定能赢！"}

	for _,sprite in pairs(bf.SpritesTable) do

 if sprite.Team == sourceSprite.Team and sprite.Role:HasTalent("心有灵犀") and sprite.Role.Key == "主角" then

result.Hp = result.Hp*1.25

--result:ClearInfo(sourceSprite)

result:AddCastInfo(sourceSprite, sourceSay1[math.random(table.getn(sourceSay1))], 1)

bf:Attack(sprite,skill,targetSprite.X,targetSprite.Y)

bf:ShowSkillAnimation(skill,targetSprite.X,targetSprite.Y,callback)

bf:Log("【"..sourceSprite.Role.Name .."】和【"..sprite.Role.Name.."】情侣天赋发动！伤害增加！")

if result.critical == false then

if (Tools.ProbabilityTest(0.25)) then

result.critical =true

bf:Log(sourceSprite.Role.Name .. "情侣天赋触发了暴击效果！")

end

end

end

--break

end

end



if(sourceSprite.Role:HasTalent("第三人格") and sourceSprite.Role.Key == "林瓶儿红衣弱" and skill.Type==1) then

local sourceSay1={"杀人这样的事情，交给我吧！"}

	for _,sprite in pairs(bf.SpritesTable) do

 if sprite.Team == sourceSprite.Team and sprite.Role.Key == "主角" then

result.Hp = result.Hp*1.0

--result:ClearInfo(sourceSprite)

result:AddCastInfo(sourceSprite, sourceSay1[math.random(table.getn(sourceSay1))], 1)

bf:Attack(sprite,skill,targetSprite.X,targetSprite.Y)

bf:ShowSkillAnimation(skill,targetSprite.X,targetSprite.Y,callback)

bf:Log("不会让任何人伤害你！")

if result.critical == false then

if (Tools.ProbabilityTest(0.99)) then

result.critical =true

bf:Log(sourceSprite.Role.Name .. "伤害暴击！")

end

end

end

--break

end

end



if(sourceSprite.Role:HasTalent("第三人格") and sourceSprite.Role.Key == "林平之" and skill.Type==1) then

local sourceSay1={"杀人这样的事情，交给我吧！"}

	for _,sprite in pairs(bf.SpritesTable) do

 if sprite.Team == sourceSprite.Team and sprite.Role.Key == "主角" then

result.Hp = result.Hp*1.0

--result:ClearInfo(sourceSprite)

result:AddCastInfo(sourceSprite, sourceSay1[math.random(table.getn(sourceSay1))], 1)

bf:Attack(sprite,skill,targetSprite.X,targetSprite.Y)

bf:ShowSkillAnimation(skill,targetSprite.X,targetSprite.Y,callback)

bf:Log("不会让任何人伤害你！")

if result.critical == false then

if (Tools.ProbabilityTest(0.99)) then

result.critical =true

bf:Log(sourceSprite.Role.Name .. "伤害暴击！")

end

end

end

--break

end

end

	

	--天赋：诅咒，100%叠加诅咒BUFF(等级5，持续5回合)

	if(sourceSprite.Role:HasTalent("诅咒")) then

		targetSprite:AddBuff("诅咒",5,5)

	end

	if(sourceSprite.Role:HasTalent("带毒")) then

		targetSprite:AddBuff("中毒",3,3)

	end

	if (targetSprite:HasBuff("毒人") and targetSprite.Team ~= sourceSprite.Team) then

		if(Tools.ProbabilityTest(0.2)) then

			result.Hp = 0

 			result.Mp = 0

 			result.costBall = 0

 			result.Critical = false

 			result.Buff:Clear()

 			result.Debuff:Clear()

			local b = math.random(100,200)

			targetSprite.Sp = targetSprite.Sp + b

			targetSprite:AttackInfo("就凭你也伤的了我？",Color.black)

		end

		sourceSprite.Hp = sourceSprite.Hp*0.9

		sourceSprite:AttackInfo("你这个毒人！呃……",Color.black)

		bf:Log(sourceSprite.Role.Name .. "被毒血反噬！")

	end



	if (targetSprite:HasBuff("女诸葛") and targetSprite.Team ~= sourceSprite.Team) then

		if(Tools.ProbabilityTest(0.1)) then

			result.Hp = 0

			result.Mp = 0

			result.costBall = 0

			result.Critical = false

			result.Buff:Clear()

			result.Debuff:Clear()

			local b = math.random(100,200)

			targetSprite.Sp = targetSprite.Sp + b

			targetSprite:AttackInfo("我早猜到了！",Color.black)

		end

	end



	--清DEBUFF天赋

if targetSprite.Role:HasTalent("毒素免疫") and targetSprite:HasBuff("中毒") then

  targetSprite:DeleteBuff("中毒")

end

if (targetSprite:HasBuff("医仙") and targetSprite.Team ~= sourceSprite.Team) then

 result.Debuff:Clear()

  targetSprite:DeleteBuff("中毒")

end

if (targetSprite:HasBuff("毒仙") and targetSprite.Team ~= sourceSprite.Team) then

 result.Debuff:Clear()

  targetSprite:DeleteBuff("中毒")

end



	--反伤天赋

if (targetSprite.Role:HasTalent("氤氲紫气") or targetSprite.Role:HasTalent("心如蛇蝎")) and sourceSprite.Team~=targetSprite.Team then

  sourceSprite.Hp=math.max(0,sourceSprite.Hp-result.Hp)

      local strmp="被〖"..targetSprite.Role.Name.."〗反伤"..tostring(result.Hp)

      targetSprite:AttackInfo(strmp,Color.red)

end



	--性别无敌天赋

if targetSprite.Role:HasTalent("倾国倾城") and sourceSprite.Team~=targetSprite.Team and sourceSprite.Role.AttributesFinal["female"]==0 then

  local rate=math.random()

  if Tools.ProbabilityTest(rate) then

    result.Hp = math.ceil(result.Hp*rate)

result:AddAttackInfo(sourceSprite, "不行……我不能伤害她……", Color.red)

  end

end

if targetSprite.Role:HasTalent("风流倜傥") and sourceSprite.Team~=targetSprite.Team and sourceSprite.Role.AttributesFinal["female"]==1 then

  local rate=math.random()

  if Tools.ProbabilityTest(rate) then

    result.Hp = math.ceil(result.Hp*rate)

result:AddAttackInfo(sourceSprite, "（好帅的男人，手脚都没有力气了呢……）", Color.magenta)

  end

end



	--减伤天赋

if targetSprite.Role:HasTalent("九玄碧落") and sourceSprite.Team~=targetSprite.Team then

  local rate=0

  if targetSprite.Team==1 then

    rate=0.1

   elseif targetSprite.Team==2 then

    rate=0.9

  end

  result.Hp = math.min(result.Hp,math.ceil(targetSprite.MaxHp*rate))

end



if targetSprite.Role:HasTalent("钢筋铁骨") and sourceSprite.Team~=targetSprite.Team then

  local rate=0

  if targetSprite.Team==1 then

    rate=0.7

   elseif targetSprite.Team==2 then

    rate=0.3

  end

  result.Hp = math.min(result.Hp,math.ceil(targetSprite.MaxHp*rate))

end



if targetSprite.Role:HasTalent("绝对屏障") and sourceSprite.Team~=targetSprite.Team then

  local rate=0

  if targetSprite.Team==1 then

    rate=0.8

   elseif targetSprite.Team==2 then

    rate=0.2

  end

  result.Hp = math.min(result.Hp,math.ceil(targetSprite.MaxHp*rate))

end



if targetSprite.Role:HasTalent("金刚不坏") and sourceSprite.Team~=targetSprite.Team then

  local rate=0

  if targetSprite.Team==1 then

    rate=0.9

   elseif targetSprite.Team==2 then

    rate=0.1

  end

  result.Hp = math.min(result.Hp,math.ceil(targetSprite.MaxHp*rate))

end



if targetSprite.Role:HasTalent("天命所归") and sourceSprite.Team~=targetSprite.Team then

  local rate=0

  if targetSprite.Team==1 then

    rate=0.9

   elseif targetSprite.Team==2 then

    rate=0.1

  end

  result.Hp = math.min(result.Hp,math.ceil(targetSprite.MaxHp*rate))

end



if targetSprite.Role:HasTalent("魔道天行") and sourceSprite.Team~=targetSprite.Team then

  local rate=0

  if targetSprite.Team==1 then

    rate=0.50

   elseif targetSprite.Team==2 then

    rate=0.50

  end

  result.Hp = math.min(result.Hp*0.8,math.ceil(targetSprite.MaxHp*rate))

end



if sourceSprite.Role:HasTalent("狂蜂浪蝶") and sourceSprite.Team~=targetSprite.Team and Tools.ProbabilityTest(0.5) then

  local rate=0

  if targetSprite.Team==1 then

    rate=0.6

   elseif targetSprite.Team==2 then

    rate=0.4

  end

  result.Hp = math.min(result.Hp,math.ceil(targetSprite.MaxHp*rate))

result:AddAttackInfo(sourceSprite, "（哎呀，胸部好碍事）", Color.magenta)

bf:Log(sourceSprite.Role.Name .. "胸部过大妨碍战斗，伤害大大减少！" ) --记录日志

end



	--妓女天赋

if targetSprite.Role:HasTalent("青楼头牌") and sourceSprite.Team~=targetSprite.Team then

  result.Hp=math.ceil(result.Hp*2.0)

bf:Log(targetSprite.Role.Name .. "因为敏感的身份而缺乏自信，受到伤害增加100%！" ) --记录日志

end

if targetSprite.Role:HasTalent("性欲旺盛") and sourceSprite.Team~=targetSprite.Team then

  result.Hp=math.ceil(result.Hp*1.2)

bf:Log(targetSprite.Role.Name .. "身体燥热难耐，受到伤害大大增加！" ) --记录日志

end

if targetSprite.Role:HasTalent("淫猥下流") and sourceSprite.Team~=targetSprite.Team then

  result.Hp=math.ceil(result.Hp*3.0)

bf:Log(targetSprite.Role.Name .. "身体燥热难耐，受到伤害大大增加！" ) --记录日志

end



	--女色狼和男色狼天赋

if sourceSprite.Role:HasTalent("采阳补阴") and sourceSprite.Team~=targetSprite.Team and targetSprite.Role.AttributesFinal["female"]==0 then

  if Tools.ProbabilityTest(0.2) then

    local hpadd=math.max(sourceSprite.MaxHp-sourceSprite.Hp,math.ceil(result.Hp*0.2))

  result.Hp=math.ceil(result.Hp*1.0)

    if hpadd>0 then

      sourceSprite.Hp=sourceSprite.Hp+hpadd

      local strhp="从〖"..targetSprite.Role.Name.."〗采补"..tostring(hpadd)

      targetSprite:AttackInfo(strhp,Color.green)

      sourceSprite:AttackInfo(sourceSprite, "嘻嘻……很好用的男人……继续嘛……", Color.magenta)

    end

  end

  if Tools.ProbabilityTest(0.2) then

    local mpadd=math.max(sourceSprite.MaxMp-sourceSprite.Mp,math.ceil(result.Hp*0.2))

  result.Hp=math.ceil(result.Hp*1.0)

    if mpadd>0 then

      sourceSprite.Mp=sourceSprite.Mp+mpadd

      local strmp="从〖"..targetSprite.Role.Name.."〗采补"..tostring(mpadd)

      targetSprite:AttackInfo(strmp,Color.blue)

    end

  end

end



if sourceSprite.Role:HasTalent("采阴补阳") and sourceSprite.Team~=targetSprite.Team and targetSprite.Role.AttributesFinal["female"]==1 then

  if Tools.ProbabilityTest(0.2) then

    local hpadd=math.max(sourceSprite.MaxHp-sourceSprite.Hp,math.ceil(result.Hp*0.2))

  result.Hp=math.ceil(result.Hp*1.0)

    if hpadd>0 then

      sourceSprite.Hp=sourceSprite.Hp+hpadd

      local strhp="从〖"..targetSprite.Role.Name.."〗采补"..tostring(hpadd)

      targetSprite:AttackInfo(strhp,Color.green)

      sourceSprite:AttackInfo(sourceSprite, "嘿嘿……女人的滋味……很不错……", Color.red)

    end

  end

  if Tools.ProbabilityTest(0.2) then

    local mpadd=math.max(sourceSprite.MaxMp-sourceSprite.Mp,math.ceil(result.Hp*0.2))

  result.Hp=math.ceil(result.Hp*1.0)

    if mpadd>0 then

      sourceSprite.Mp=sourceSprite.Mp+mpadd

      local strmp="从〖"..targetSprite.Role.Name.."〗采补"..tostring(mpadd)

      targetSprite:AttackInfo(strmp,Color.blue)

    end

  end

end



	--几率眩晕

if sourceSprite.Role:HasTalent("黯然神伤") and sourceSprite.Team~=targetSprite.Team and Tools.ProbabilityTest(0.3) then

  if sourceSprite:AddBuffOnly2("晕眩",0,1)==true then

bf:Log("<color=green>〖"..sourceSprite.Role.Name.."〗唔……为什么……为什么……</color>")

result:AddAttackInfo(sourceSprite, "哎……为什么……会这样……", Color.green)

bf:Log(sourceSprite.Role.Name .. "在过招时想到了悲伤往事，眩晕一回合。" ) --记录日志

  end

end



if sourceSprite.Role:HasTalent("寒冰领域") and sourceSprite.Team~=targetSprite.Team then

  if Tools.ProbabilityTest(0.25) then

    if targetSprite:AddBuffOnly2("晕眩",0,1)==true then

      sourceSprite:AttackInfo("寒冰极意！",Color.blue)

bf:Log(targetSprite.Role.Name .. "在过招时被阴寒之力所伤，眩晕一回合。" ) --记录日志

    end

  end

end



	--倒霉鬼天赋

if sourceSprite.Role:HasTalent("天煞孤星") and sourceSprite.Team~=targetSprite.Team and Tools.ProbabilityTest(0.5) then

  result.Hp=math.ceil(result.Hp*0.5)

result:AddAttackInfo(sourceSprite, "哎，真倒霉……", Color.green)

bf:Log(sourceSprite.Role.Name .. "运气一如既往的差，伤害减半！" ) --记录日志

end





	--赌徒天赋

if targetSprite.Role:HasTalent("兔起鹘落") and sourceSprite.Team~=targetSprite.Team then

  local rate=0.2

  if Tools.ProbabilityTest(0.5) then

    rate=rate+0.3

    if Tools.ProbabilityTest(0.4) then

      rate=rate+0.5

    end

  end

  result.Hp=math.ceil(result.Hp*(1-rate))

end



	--女魅魔战斗吸蓝天赋

	if (sourceSprite.Role:HasTalent("吸精魔功")) then

		if(sourceSprite.Role.AttributesFinal["female"] == 1 and targetSprite.Role.AttributesFinal["female"] == 0) then

			local mm1 = result.Hp * (Tools.GetRandomInt(3,7) * 0.2)

			local mm2 = tonumber(sourceSprite.Role.AttributesFinal["wuxing"])

			local mm3 = mm1 + 2*mm2

			local mm4 = mm2 * 0.002 + 0.55

			if sourceSprite.Role.Key ~= "主角" then

				mm4 = 0.95

			end

			if (Tools.ProbabilityTest(mm4)) then

				targetSprite.Mp = targetSprite.Mp - mm3

				sourceSprite.Mp = sourceSprite.Mp + mm3

				targetSprite:AttackInfo("被吸取精力-"..mm3, Color.black)

				sourceSprite:AttackInfo("吸取精力+"..mm3, Color.blue)

        	end

		end

	end



	if (skill.Name == "圣手回天") then

		if (sourceSprite.Team == targetSprite.Team) then

			local addhp1 = math.min(math.floor(targetSprite.MaxHp * 0.3), tonumber(targetSprite.MaxHp - targetSprite.Hp))

			if (addhp1 > 0) then

				targetSprite.Hp = tonumber(targetSprite.Hp + addhp1)

			end

			result:AddAttackInfo(targetSprite, "+" .. tostring(addhp1), Color.green)

			bf:Log(sourceSprite.Role.Name .. "【圣手回天】发动，" .. targetSprite.Role.Name .. "恢复30%生命" ) --记录日志

		end

	elseif (skill.Name == "梅花三弄") then

		if (sourceSprite.Team == targetSprite.Team) then

			local addhp1 = math.min(math.floor(targetSprite.MaxHp * 0.15), tonumber(targetSprite.MaxHp - targetSprite.Hp))

			if (addhp1 > 0) then

				targetSprite.Hp = tonumber(targetSprite.Hp + addhp1)

			end

			result:AddAttackInfo(targetSprite, "+" .. tostring(addhp1), Color.green)

			bf:Log(sourceSprite.Role.Name .. "【梅花三弄】发动，" .. targetSprite.Role.Name .. "恢复15%生命" ) --记录日志

		end

	elseif (skill.Name == "百草凝魂") then

		if (sourceSprite.Team == targetSprite.Team) then

			result:AddCastInfo(sourceSprite, "复活吧，我的勇士！", 1)

			result:AddCastInfo(targetSprite, "为你而战，我的女士！", 1)			

			if (RuntimeData.Instance:resurrection(0, sourceSprite.Team, 0.2, 0.1) == 0) then

				local addhp2 = math.min(math.floor(sourceSprite.MaxHp * 0.1), tonumber(sourceSprite.MaxHp - sourceSprite.Hp))

				if (addhp2 > 0) then

					sourceSprite.Hp = tonumber(sourceSprite.Hp + addhp2)

					result:AddAttackInfo(sourceSprite, "+" .. tostring(addhp2), Color.green)

				end

			end

		end

	end

if(skill.Name == "无中生有"  and targetSprite.Team ~= sourceSprite.Team) then

local toudao=0

local typ={}

result:AddAttackInfo(sourceSprite, "看我神手无双", Color.magenta)

local sourceSayFL1 = {"谢谢老板！银两收下了！","我凭本事偷到的，为什么要还？"}

local sourceSaySI1={"神兵利器，有德者得！","嘿嘿，这下不用赤手空拳了"}

local sourceSaySI2={"这衣服本大侠也要来一件","岂曰无衣？与子同袍！"}

local sourceSaySI3={"宝贝别藏着，给我瞅瞅","我也看上这宝贝了"}

local sourceSaySI4={"我来抄书了","读书读书，多多益善！"}

if (Tools.ProbabilityTest(0.8)) then

for i=1,4 do

local sitem=targetSprite.Role:GetEquipment(i)

if  sitem  then

table.insert(typ,i)

end

end

if #typ>0 then

toudao =typ[math.random(table.getn(typ))]

local fsitem=targetSprite.Role:GetEquipment(toudao)

RuntimeData.Instance:addItem(fsitem,1)

--targetRole.Equipment:Remove(fsitem)

else

toudao=200*targetSprite.Role.Level

end

else

toudao=100*targetSprite.Role.Level

end

				if (tonumber(toudao)==1) then					result:AddCastInfo(sourceSprite,sourceSaySI1[math.random(table.getn(sourceSaySI1))], 1)

					result:AddAttackInfo(targetSprite, "被复制武器", Color.white)

					bf:Log(sourceSprite.Role.Name .. "成功复制" .. targetSprite.Role.Name .. "的【武器】！")

				elseif (tonumber(toudao)==2) then

result:AddCastInfo(sourceSprite,sourceSaySI2[math.random(table.getn(sourceSaySI2))], 1)

					result:AddAttackInfo(targetSprite, "被复制防具", Color.white)

					bf:Log(sourceSprite.Role.Name .. "成功复制" .. targetSprite.Role.Name .. "的【防具】！")

				elseif (tonumber(toudao)==3) then

result:AddCastInfo(sourceSprite,sourceSaySI3[math.random(table.getn(sourceSaySI3))], 1)

					result:AddAttackInfo(targetSprite, "被复制饰品", Color.white)

					bf:Log(sourceSprite.Role.Name .. "成功复制" .. targetSprite.Role.Name .. "的【饰品】！")

				elseif (tonumber(toudao)==4) then

result:AddCastInfo(sourceSprite,sourceSaySI4[math.random(table.getn(sourceSaySI4))], 1)

					result:AddAttackInfo(targetSprite, "被复制经书", Color.white)

					bf:Log(sourceSprite.Role.Name .. "成功复制" .. targetSprite.Role.Name .. "的【经书】！")

				else

RuntimeData.Instance.Money = RuntimeData.Instance.Money + tonumber(toudao)

					result:AddCastInfo(sourceSprite, sourceSayFL1[math.random(table.getn(sourceSayFL1))], 1)

					result:AddAttackInfo(targetSprite, "被偷取$" .. tostring(toudao), Color.white)

					result:AddAttackInfo(sourceSprite, "+$" .. tostring(toudao), Color.green)

					if(math.random() <= 0.2) then

						local targetSayFL1 = {"草！","不要脸！","银两还给我啊！"}

						result:AddCastInfo(targetSprite, targetSayFL1[math.random(table.getn(targetSayFL1))], 1)

					end

					bf:Log(sourceSprite.Role.Name .. "成功偷取" .. targetSprite.Role.Name .. "的银两【" .. tostring(toudao) .. "】！")

					end

local mint = math.abs(sourceSprite.Role.AttributesFinal["shenfa"]+sourceSprite.Role.AttributesFinal["bili"])

targetSprite.Hp =math.max(targetSprite.Hp- math.min(100 + mint*100,targetSprite.MaxHp*0.3),1)

bf:Log(targetSprite.Role.Name .."被【"..sourceSprite.Role.Name.."】附加伤害！")

			end



	return result	

end









--扩展天赋3（包含在AI计算内，并参与计算公式）

--与扩展天赋区别是参与了最小攻击、最大攻击、暴击、防御相关的计算公式，但请不要对result.Hp进行赋值

function AttackLogic_extendTalents3(sourceSprite, targetSprite, skill, bf, result, formula)

	--天赋：女性（特定武功对男性的攻击加倍）

	if(sourceSprite.Role:HasTalent("女性") and targetSprite.Role.AttributesFinal["female"] == 0) then 

		if ( skill.Name == "摄魂魔音") or ( skill.Name == "男人见不得") or ( skill.Name == "绝户虎爪手") then

			formula.attackLow = formula.attackLow * 2

			if (formula.attackLow < 0) then

				formula.attackLow = 0

			end

			formula.attackUp = formula.attackUp * 2

		end

   return

	end

	--天赋：小家碧玉（最小攻击降低30%，最大攻击提高200%）

	if(sourceSprite.Role:HasTalent("小家碧玉") and skill.Name == "花拳绣腿") then

		formula.attackLow = formula.attackLow * 0.7

        if (formula.attackLow < 0) then

            formula.attackLow = 0

        end

 		formula.attackUp = formula.attackUp * 3.0

	return

	end

	--自定义攻防类

	if(targetSprite.Role:HasTalent("病娇")) then

		formula.defence = formula.defence * 0.7

		formula.criticalHit = formula.criticalHit + 0.2

	return

	end

	if(targetSprite.Role:HasTalent("孕妇")) then

		formula.defence = formula.defence * 0.2

	return

	end

	if(targetSprite.Role:HasTalent("萝莉")) then

		formula.defence = formula.defence * 0.85

	return

	end

	if(targetSprite.Role:HasTalent("冰肌玉骨")) then

		formula.defence = formula.defence * 0.9

 		formula.attackUp = formula.attackUp * 1.15

	return

	end

	if(targetSprite.Role:HasTalent("正太")) then

		formula.defence = formula.defence * 0.85

	return

	end

	if(targetSprite.Role:HasTalent("正气凛然")) then

		formula.defence = formula.defence * 1.2

	return

	end

	if(sourceSprite.Role:HasTalent("傲娇")) then

		formula.criticalHit = formula.criticalHit + 0.2

	return

	end

	if(targetSprite.Role:HasTalent("专业掰弯")) then

		formula.defence = formula.defence + 100

		formula.defence = formula.defence * 1.1

    return

	end



if sourceSprite.Role:HasTalent("九阴真气") and sourceSprite.Team~=targetSprite.Team then

  formula.attackUp=formula.attackUp*1.15

end



if targetSprite.Role:HasTalent("九阳护体") and sourceSprite.Team~=targetSprite.Team then

  formula.defence=formula.defence*1.2

end





	if(targetSprite.Role:HasTalent("天下无双")) then

  		if (formula.criticalHit < 2.0) then

			formula.criticalHit = 2.0

		end

		formula.attackLow = math.abs(formula.attackLow * 1.2)

		formula.attackUp = math.abs(formula.attackUp * 2.0)

	return

	end

end

