# 游戏文件加密解密工具

## 🎯 项目简介

这是一个专门为金庸群侠传X游戏开发的文件加密解密工具，可以帮助你：
- ✅ 一键解密游戏中的Lua脚本和XML配置文件
- ✅ 编辑修改游戏逻辑和配置
- ✅ 重新加密文件以便游戏识别
- ✅ 批量处理整个目录
- ✅ 图形化界面，操作简单

## 📦 工具包内容

```
游戏加密解密工具/
├── game_crypto_tool.py      # 主程序（图形界面）
├── 启动工具.bat             # 一键启动脚本
├── 安装依赖.bat             # 依赖安装脚本
├── README.md                # 项目说明（本文件）
├── 快速开始.md              # 快速上手指南
├── 使用说明.md              # 详细使用说明
└── test_encrypt.py          # 测试脚本
```

## 🚀 快速开始

### 1. 环境准备
- 确保安装了 Python 3.6 或更高版本
- 双击运行 `安装依赖.bat` 安装必要的库

### 2. 启动工具
- 双击 `启动工具.bat` 启动图形界面
- 或者运行命令：`python game_crypto_tool.py`

### 3. 解密文件
1. 选择游戏目录中的加密文件或目录
2. 选择输出目录
3. 选择"解密"操作
4. 点击"开始处理"

### 4. 编辑和重新加密
1. 编辑解密后的文件
2. 选择修改后的文件
3. 选择"加密"操作
4. 将加密后的文件替换回游戏目录

## 🔧 技术原理

### 加密算法
- **加密方式**：3DES ECB模式
- **密钥**：字符串 "Yh$45Ct@mods" 的MD5哈希值
- **编码**：Base64编码
- **填充**：PKCS7填充
- **格式**：`文件长度@Base64编码的加密数据`

### 支持的文件类型
- `.lua` - Lua脚本文件（游戏逻辑）
- `.xml` - XML配置文件
- 其他使用相同加密方式的文件

## 📋 功能特性

### 核心功能
- [x] 单文件解密/加密
- [x] 批量目录处理
- [x] 图形化界面
- [x] 详细操作日志
- [x] 进度显示
- [x] 错误处理

### 安全特性
- [x] 自动备份提醒
- [x] 文件格式验证
- [x] 加密解密一致性验证
- [x] 详细错误信息

## 🎮 游戏文件说明

### 主要Lua文件
- `main.lua` - 主配置文件
- `skill.lua` - 技能逻辑扩展
- `battle.lua` - 战斗系统
- `AI.lua` - 战斗AI扩展
- `item.lua` - 物品系统
- `GameEngine.lua` - 游戏引擎扩展

### 常见修改场景
- 修改技能伤害和效果
- 调整角色属性
- 添加新的游戏功能
- 修改AI行为
- 调整物品属性

## 📖 使用示例

### 解密所有Lua文件
```bash
# 命令行方式
python game_crypto_tool.py gamedata/modcache/HYYM/lua -d -o 解密文件
```

### 重新加密修改后的文件
```bash
# 命令行方式  
python game_crypto_tool.py 修改后的文件 -e -o 加密文件
```

## ⚠️ 注意事项

### 重要提醒
- 🔴 **务必备份原始文件**
- 🔴 **先在测试环境验证修改**
- 🔴 **不要直接在游戏目录操作**

### 最佳实践
1. 创建专门的工作目录
2. 保持原始文件备份
3. 一次只修改一个文件
4. 记录修改内容
5. 测试后再应用到游戏

## 🐛 故障排除

### 常见问题

**Q: 工具无法启动**
A: 检查Python环境，运行 `安装依赖.bat`

**Q: 解密失败**
A: 确认文件格式正确，查看详细错误日志

**Q: 游戏无法识别修改后的文件**
A: 检查重新加密的文件格式和位置

**Q: 中文显示乱码**
A: 确保使用UTF-8编码保存文件

## 📊 测试验证

工具已通过以下测试：
- ✅ 基本加密解密功能
- ✅ 真实游戏文件处理
- ✅ 批量目录处理
- ✅ 中文内容支持
- ✅ 大文件处理
- ✅ 错误处理机制

## 🔄 版本历史

### v1.0.0 (当前版本)
- 实现基本的加密解密功能
- 图形化界面
- 批量处理支持
- 完整的错误处理
- 详细的使用文档

## 📞 支持与反馈

如果遇到问题或有改进建议：
1. 查看详细的使用说明文档
2. 检查操作日志中的错误信息
3. 确认文件格式和路径正确

## 📄 许可证

本工具仅供学习和个人使用，请遵守相关法律法规。

---

**祝你游戏修改愉快！** 🎮✨
