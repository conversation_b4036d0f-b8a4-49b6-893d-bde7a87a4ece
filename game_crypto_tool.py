#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏文件加密解密工具
支持一键解密和重新加密
"""

import os
import sys
import base64
import hashlib
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from Crypto.Cipher import DES3
from Crypto.Util.Padding import pad, unpad
import threading

class GameCryptoTool:
    def __init__(self):
        # 从SaveManager代码中提取的密钥
        self.des3_key = hashlib.md5("Yh$45Ct@mods".encode('utf-8')).digest()
        
        # 创建GUI
        self.root = tk.Tk()
        self.root.title("游戏文件加密解密工具")
        self.root.geometry("800x600")
        
        self.setup_gui()
        
    def setup_gui(self):
        """设置GUI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="游戏文件加密解密工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 文件/目录选择
        ttk.Label(main_frame, text="选择文件或目录:").grid(row=1, column=0, sticky=tk.W, pady=5)
        
        self.path_var = tk.StringVar()
        path_entry = ttk.Entry(main_frame, textvariable=self.path_var, width=60)
        path_entry.grid(row=1, column=1, padx=(10, 5), pady=5)
        
        ttk.Button(main_frame, text="浏览文件", command=self.browse_file).grid(row=1, column=2, padx=5, pady=5)
        ttk.Button(main_frame, text="浏览目录", command=self.browse_directory).grid(row=1, column=3, padx=5, pady=5)
        
        # 输出目录选择
        ttk.Label(main_frame, text="输出目录:").grid(row=2, column=0, sticky=tk.W, pady=5)
        
        self.output_var = tk.StringVar()
        output_entry = ttk.Entry(main_frame, textvariable=self.output_var, width=60)
        output_entry.grid(row=2, column=1, padx=(10, 5), pady=5)
        
        ttk.Button(main_frame, text="选择目录", command=self.browse_output).grid(row=2, column=2, padx=5, pady=5)
        
        # 操作选择
        ttk.Label(main_frame, text="操作类型:").grid(row=3, column=0, sticky=tk.W, pady=5)
        
        self.operation_var = tk.StringVar(value="decrypt")
        operation_frame = ttk.Frame(main_frame)
        operation_frame.grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        ttk.Radiobutton(operation_frame, text="解密", variable=self.operation_var, value="decrypt").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(operation_frame, text="加密", variable=self.operation_var, value="encrypt").pack(side=tk.LEFT)
        
        # 文件类型选择
        ttk.Label(main_frame, text="文件类型:").grid(row=4, column=0, sticky=tk.W, pady=5)
        
        self.filetype_var = tk.StringVar(value="lua")
        filetype_frame = ttk.Frame(main_frame)
        filetype_frame.grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        ttk.Radiobutton(filetype_frame, text="Lua文件", variable=self.filetype_var, value="lua").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(filetype_frame, text="XML文件", variable=self.filetype_var, value="xml").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(filetype_frame, text="所有文件", variable=self.filetype_var, value="all").pack(side=tk.LEFT)
        
        # 执行按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=4, pady=20)
        
        self.execute_button = ttk.Button(button_frame, text="开始处理", command=self.execute_operation)
        self.execute_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="退出", command=self.root.quit).pack(side=tk.LEFT)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=6, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=5)
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.grid(row=7, column=0, columnspan=4, pady=5)
        
        # 日志区域
        ttk.Label(main_frame, text="操作日志:").grid(row=8, column=0, sticky=tk.W, pady=(10, 5))
        
        self.log_text = scrolledtext.ScrolledText(main_frame, height=15, width=80)
        self.log_text.grid(row=9, column=0, columnspan=4, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(9, weight=1)
        
    def browse_file(self):
        """浏览文件"""
        filename = filedialog.askopenfilename(
            title="选择要处理的文件",
            filetypes=[("Lua文件", "*.lua"), ("XML文件", "*.xml"), ("所有文件", "*.*")]
        )
        if filename:
            self.path_var.set(filename)
            
    def browse_directory(self):
        """浏览目录"""
        dirname = filedialog.askdirectory(title="选择要处理的目录")
        if dirname:
            self.path_var.set(dirname)
            
    def browse_output(self):
        """浏览输出目录"""
        dirname = filedialog.askdirectory(title="选择输出目录")
        if dirname:
            self.output_var.set(dirname)
            
    def log(self, message):
        """添加日志"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update()
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        
    def decrypt_file(self, file_path):
        """解密单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            # 检查是否有长度前缀
            if '@' not in content:
                return None, "文件格式不正确，没有找到长度前缀"
            
            parts = content.split('@', 1)
            if len(parts) != 2 or not parts[0].isdigit():
                return None, "文件格式不正确，长度前缀无效"
            
            expected_length = int(parts[0])
            encrypted_str = parts[1]
            
            # Base64解码
            padded_str = encrypted_str
            while len(padded_str) % 4 != 0:
                padded_str += '='
            
            raw_data = base64.b64decode(padded_str)
            
            # 3DES解密
            if len(raw_data) % 8 != 0:
                truncated_length = (len(raw_data) // 8) * 8
                raw_data = raw_data[:truncated_length]
            
            cipher = DES3.new(self.des3_key, DES3.MODE_ECB)
            decrypted_data = cipher.decrypt(raw_data)
            
            # 移除填充
            try:
                unpadded_data = unpad(decrypted_data, 8)
                decrypted_data = unpadded_data
            except:
                pass
            
            # 解码为字符串
            final_result = decrypted_data.decode('utf-8')
            
            return final_result, None
            
        except Exception as e:
            return None, f"解密失败: {str(e)}"
    
    def encrypt_file(self, content):
        """加密文件内容"""
        try:
            # UTF-8编码
            data = content.encode('utf-8')
            
            # 添加填充
            padded_data = pad(data, 8)
            
            # 3DES加密
            cipher = DES3.new(self.des3_key, DES3.MODE_ECB)
            encrypted_data = cipher.encrypt(padded_data)
            
            # Base64编码
            encoded_data = base64.b64encode(encrypted_data).decode('ascii')
            
            # 添加长度前缀
            length = len(content)
            final_result = f"{length}@{encoded_data}"
            
            return final_result, None
            
        except Exception as e:
            return None, f"加密失败: {str(e)}"

    def get_file_extensions(self):
        """获取要处理的文件扩展名"""
        filetype = self.filetype_var.get()
        if filetype == "lua":
            return ['.lua']
        elif filetype == "xml":
            return ['.xml']
        else:
            return ['.lua', '.xml']

    def process_single_file(self, file_path, output_path):
        """处理单个文件"""
        operation = self.operation_var.get()

        try:
            if operation == "decrypt":
                # 解密
                result, error = self.decrypt_file(file_path)
                if error:
                    self.log(f"❌ {os.path.basename(file_path)}: {error}")
                    return False

                # 保存解密结果
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(result)

                self.log(f"✅ 解密成功: {os.path.basename(file_path)}")
                return True

            else:
                # 加密
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                result, error = self.encrypt_file(content)
                if error:
                    self.log(f"❌ {os.path.basename(file_path)}: {error}")
                    return False

                # 保存加密结果
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(result)

                self.log(f"✅ 加密成功: {os.path.basename(file_path)}")
                return True

        except Exception as e:
            self.log(f"❌ {os.path.basename(file_path)}: {str(e)}")
            return False

    def execute_operation(self):
        """执行操作"""
        input_path = self.path_var.get().strip()
        output_path = self.output_var.get().strip()

        if not input_path:
            messagebox.showerror("错误", "请选择输入文件或目录")
            return

        if not os.path.exists(input_path):
            messagebox.showerror("错误", "输入路径不存在")
            return

        if not output_path:
            messagebox.showerror("错误", "请选择输出目录")
            return

        # 在新线程中执行操作
        thread = threading.Thread(target=self._execute_operation_thread, args=(input_path, output_path))
        thread.daemon = True
        thread.start()

    def _execute_operation_thread(self, input_path, output_path):
        """在线程中执行操作"""
        try:
            self.execute_button.config(state='disabled')
            self.status_var.set("正在处理...")
            self.progress_var.set(0)

            operation = self.operation_var.get()
            extensions = self.get_file_extensions()

            # 收集要处理的文件
            files_to_process = []

            if os.path.isfile(input_path):
                # 单个文件
                files_to_process.append(input_path)
            else:
                # 目录
                for root, dirs, files in os.walk(input_path):
                    for file in files:
                        if any(file.lower().endswith(ext) for ext in extensions):
                            files_to_process.append(os.path.join(root, file))

            if not files_to_process:
                self.log("❌ 没有找到要处理的文件")
                return

            self.log(f"📁 找到 {len(files_to_process)} 个文件需要处理")

            # 创建输出目录
            if not os.path.exists(output_path):
                os.makedirs(output_path)

            success_count = 0
            total_count = len(files_to_process)

            for i, file_path in enumerate(files_to_process):
                # 计算相对路径
                if os.path.isfile(input_path):
                    # 单个文件
                    rel_path = os.path.basename(file_path)
                else:
                    # 目录中的文件
                    rel_path = os.path.relpath(file_path, input_path)

                # 确定输出文件路径
                if operation == "decrypt":
                    # 解密时，移除可能的加密后缀，添加_decrypted
                    base_name = os.path.splitext(rel_path)[0]
                    ext = os.path.splitext(rel_path)[1]
                    output_file = os.path.join(output_path, f"{base_name}_decrypted{ext}")
                else:
                    # 加密时，移除可能的_decrypted后缀
                    if rel_path.endswith('_decrypted.lua'):
                        rel_path = rel_path.replace('_decrypted.lua', '.lua')
                    elif rel_path.endswith('_decrypted.xml'):
                        rel_path = rel_path.replace('_decrypted.xml', '.xml')
                    output_file = os.path.join(output_path, rel_path)

                # 创建输出目录
                output_dir = os.path.dirname(output_file)
                if not os.path.exists(output_dir):
                    os.makedirs(output_dir)

                # 处理文件
                if self.process_single_file(file_path, output_file):
                    success_count += 1

                # 更新进度
                progress = (i + 1) / total_count * 100
                self.progress_var.set(progress)
                self.status_var.set(f"处理中... {i + 1}/{total_count}")

            # 完成
            self.log(f"🎉 处理完成！成功: {success_count}/{total_count}")
            self.status_var.set(f"完成 - 成功: {success_count}/{total_count}")

            if success_count > 0:
                messagebox.showinfo("完成", f"处理完成！\n成功: {success_count}/{total_count}\n输出目录: {output_path}")

        except Exception as e:
            self.log(f"❌ 处理过程中出错: {str(e)}")
            messagebox.showerror("错误", f"处理过程中出错: {str(e)}")

        finally:
            self.execute_button.config(state='normal')
            if self.progress_var.get() < 100:
                self.progress_var.set(0)

    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    app = GameCryptoTool()
    app.run()

if __name__ == "__main__":
    main()
