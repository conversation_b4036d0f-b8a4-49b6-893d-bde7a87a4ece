# 快速开始指南

## 🚀 一键解密所有游戏文件

### 第一步：解密游戏文件
1. 双击运行 `启动工具.bat`
2. 在界面中：
   - **选择目录**：点击"浏览目录"，选择游戏的lua目录（如：`gamedata/modcache/HYYM/lua/`）
   - **输出目录**：点击"选择目录"，选择一个空文件夹（如：`解密后的文件/`）
   - **操作类型**：选择"解密"
   - **文件类型**：选择"所有文件"
3. 点击"开始处理"
4. 等待处理完成，你会在输出目录中看到所有解密后的文件

### 第二步：编辑文件
现在你可以用任何文本编辑器打开解密后的文件进行修改：
- `main_decrypted.lua` - 主配置文件
- `skill_decrypted.lua` - 技能逻辑
- `battle_decrypted.lua` - 战斗系统
- 等等...

### 第三步：重新加密修改后的文件
1. 修改完文件后，再次运行工具
2. 在界面中：
   - **选择目录**：选择包含修改后文件的目录
   - **输出目录**：选择一个新的文件夹（如：`重新加密的文件/`）
   - **操作类型**：选择"加密"
   - **文件类型**：选择"所有文件"
3. 点击"开始处理"
4. 将加密后的文件复制回游戏目录

## 📁 推荐的文件夹结构

```
游戏修改工作区/
├── 原始文件备份/          # 备份原始加密文件
├── 解密后的文件/          # 解密后的明文文件（用于编辑）
├── 修改后的文件/          # 你修改过的文件
├── 重新加密的文件/        # 重新加密后的文件（用于替换游戏文件）
└── 工具/
    ├── game_crypto_tool.py
    ├── 启动工具.bat
    └── 使用说明.md
```

## ⚡ 常用操作示例

### 修改技能伤害
1. 解密 `skill.lua`
2. 编辑 `skill_decrypted.lua`，找到相关技能代码
3. 修改伤害数值
4. 重新加密为 `skill.lua`
5. 替换游戏目录中的原文件

### 修改角色属性
1. 解密 `main.lua` 或相关配置文件
2. 编辑属性数值
3. 重新加密并替换

### 添加新功能
1. 解密相关的lua文件
2. 添加你的代码
3. 重新加密并替换

## 🛡️ 安全提醒

- ✅ 始终备份原始文件
- ✅ 在测试环境中先验证修改
- ✅ 一次只修改一个文件，便于排错
- ❌ 不要直接在游戏目录中操作
- ❌ 不要删除原始备份文件

## 🔧 故障排除

### 问题：工具无法启动
**解决方案**：
1. 确保安装了Python 3.6+
2. 运行：`pip install pycryptodome`
3. 如果还有问题，直接运行：`python game_crypto_tool.py`

### 问题：解密失败
**解决方案**：
1. 确认选择的是正确的加密文件
2. 检查文件是否已经是明文格式
3. 查看日志中的详细错误信息

### 问题：游戏无法识别修改后的文件
**解决方案**：
1. 确认重新加密的文件格式正确
2. 检查文件名是否正确
3. 确认文件被正确放置在游戏目录中

## 🎯 高级技巧

### 批量处理
- 可以选择整个目录进行批量解密/加密
- 工具会自动处理子目录中的文件
- 支持同时处理多种文件类型

### 文件对比
- 使用文本对比工具（如Beyond Compare）对比原始和修改后的文件
- 这样可以清楚看到你做了哪些修改

### 版本管理
- 为每次修改创建不同的文件夹
- 记录修改内容和日期
- 便于回滚和管理多个版本

## 📞 需要帮助？

如果遇到问题：
1. 查看 `使用说明.md` 获取详细信息
2. 检查工具的操作日志
3. 确认文件路径和格式正确
4. 尝试重新启动工具

祝你游戏修改愉快！🎮
