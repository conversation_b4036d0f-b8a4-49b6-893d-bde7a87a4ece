#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏文件解密工具
支持解密Lua脚本、XML文件和存档文件
"""

import os
import sys
import base64
import hashlib
from Crypto.Cipher import DES3
from Crypto.Util.Padding import unpad
import argparse

class GameDecryptor:
    def __init__(self):
        # 从SaveManager代码中提取的密钥
        self.des3_key = hashlib.md5("Yh$45Ct@mods".encode('utf-8')).digest()
        
    def extract_string(self, encrypted_str):
        """
        对应SaveManager.ExtractString方法
        用于解密Lua文件
        根据C#代码: str.Replace('\\', '0').Replace('_', '1').Substring(1, str.Length - 2).Replace("/", "").Replace("#", "/")
        """
        try:
            # 检查是否有长度前缀
            if '@' in encrypted_str:
                parts = encrypted_str.split('@', 1)
                if len(parts) == 2 and parts[0].isdigit():
                    # 移除长度前缀
                    encrypted_str = parts[1]

            # 按照C#代码的顺序处理
            # 1. 替换特殊字符
            processed = encrypted_str.replace('\\', '0').replace('_', '1')

            # 2. 移除首尾字符 Substring(1, str.Length - 2)
            if len(processed) >= 3:
                processed = processed[1:-1]

            # 3. 替换路径分隔符
            processed = processed.replace("/", "").replace("#", "/")

            # 4. Base64解码
            try:
                decoded = base64.b64decode(processed)
                decoded_str = decoded.decode('utf-8')
            except:
                # 如果UTF-8解码失败，尝试其他编码
                decoded_str = decoded.decode('latin-1')

            # 5. 解压缩字符串 - 每两个字符为一组，第一个是字符，第二个是重复次数
            result = []
            i = 0
            while i < len(decoded_str) - 1:
                char = decoded_str[i]
                try:
                    count = int(decoded_str[i + 1])
                    result.append(char * count)
                except ValueError:
                    # 如果不是数字，直接添加字符
                    result.append(char)
                i += 2

            return ''.join(result)
        except Exception as e:
            print(f"ExtractString解密失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def des3_decrypt(self, encrypted_data):
        """
        3DES解密方法
        对应SaveManager.m方法
        """
        try:
            cipher = DES3.new(self.des3_key, DES3.MODE_ECB)
            decrypted = cipher.decrypt(base64.b64decode(encrypted_data))
            # 移除PKCS7填充
            decrypted = unpad(decrypted, DES3.block_size)
            return decrypted.decode('utf-8')
        except Exception as e:
            print(f"3DES解密失败: {e}")
            return None
    
    def crc_decrypt(self, encrypted_str):
        """
        对应SaveManager.crcm方法
        带CRC校验的解密
        """
        try:
            parts = encrypted_str.split('@')
            if len(parts) != 2:
                return None
                
            crc_expected = parts[0]
            encrypted_content = parts[1]
            
            # 先解密内容
            decrypted = self.des3_decrypt(encrypted_content)
            if decrypted is None:
                return None
                
            # 验证CRC
            crc_actual = self.calculate_crc16(decrypted)
            if crc_expected != crc_actual:
                print(f"CRC校验失败: 期望{crc_expected}, 实际{crc_actual}")
                return None
                
            return decrypted
        except Exception as e:
            print(f"CRC解密失败: {e}")
            return None
    
    def calculate_crc16(self, data):
        """
        对应SaveManager.CRC16_C方法
        """
        data_bytes = data.encode('utf-8')
        crc_high = 0xFF
        crc_low = 0xFF
        
        for byte in data_bytes:
            crc_high ^= byte
            for _ in range(8):
                temp_high = crc_low
                temp_low = crc_high
                crc_low >>= 1
                crc_high >>= 1
                
                if temp_high & 1:
                    crc_high |= 0x80
                    
                if temp_low & 1:
                    crc_low ^= 0xA0
                    crc_high ^= 0x01
                    
        return f"{crc_low}{crc_high}"
    
    def get_result_transform(self, text):
        """
        对应SaveManager.getResult方法
        字符转换
        """
        upper_chars = list('QWERTYUIOPASDFGHJKLZXCVBNM')
        lower_chars = list('qwertyuiopasdfghjklzxcvbnm')
        
        result = []
        for char in text:
            if char in upper_chars:
                idx = upper_chars.index(char)
                result.append(lower_chars[idx])
            elif char in lower_chars:
                idx = lower_chars.index(char)
                result.append(upper_chars[idx])
            else:
                result.append(char)
                
        return ''.join(result)
    
    def decode_save(self, encrypted_str):
        """
        对应SaveManager.Decode_Save方法
        存档文件解密
        """
        try:
            # 处理结尾的@符号
            if encrypted_str.endswith('@'):
                encrypted_str = encrypted_str[:-1] + '='
                
            # 移除开头的@
            if encrypted_str.startswith('@'):
                encrypted_str = encrypted_str[1:]
                
            # 移除#号并替换$为/
            encrypted_str = encrypted_str.replace('#', '').replace('$', '/')
            
            # 字符转换
            encrypted_str = self.get_result_transform(encrypted_str)
            
            # Base64解码
            return base64.b64decode(encrypted_str).decode('utf-8')
        except Exception as e:
            print(f"存档解密失败: {e}")
            return None
    
    def decrypt_file(self, file_path, output_path=None):
        """
        解密文件
        """
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return False
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                
            decrypted = None
            
            # 判断文件类型并选择解密方法
            if content.startswith('@') and len(content) > 100:
                # Lua文件或其他ExtractString加密的文件
                print("检测到ExtractString加密格式，尝试解密...")
                decrypted = self.extract_string(content)
                
            elif '@' in content and content.count('@') == 1:
                # CRC加密格式
                print("检测到CRC加密格式，尝试解密...")
                decrypted = self.crc_decrypt(content)
                
            elif content.startswith('@'):
                # 存档文件格式
                print("检测到存档加密格式，尝试解密...")
                decrypted = self.decode_save(content)
                
            else:
                print("未识别的加密格式或文件已是明文")
                return False
                
            if decrypted is None:
                print("解密失败")
                return False
                
            # 确定输出文件路径
            if output_path is None:
                base_name = os.path.splitext(file_path)[0]
                ext = os.path.splitext(file_path)[1]
                output_path = f"{base_name}_decrypted{ext}"
                
            # 写入解密后的内容
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(decrypted)
                
            print(f"解密成功: {output_path}")
            return True
            
        except Exception as e:
            print(f"解密过程出错: {e}")
            return False
    
    def decrypt_directory(self, dir_path, output_dir=None):
        """
        批量解密目录中的文件
        """
        if not os.path.exists(dir_path):
            print(f"目录不存在: {dir_path}")
            return
            
        if output_dir is None:
            output_dir = dir_path + "_decrypted"
            
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        success_count = 0
        total_count = 0
        
        for root, dirs, files in os.walk(dir_path):
            for file in files:
                if file.endswith(('.lua', '.xml', '.txt', '.dat', '.save')):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, dir_path)
                    output_file = os.path.join(output_dir, rel_path)
                    
                    # 创建输出目录
                    output_file_dir = os.path.dirname(output_file)
                    if not os.path.exists(output_file_dir):
                        os.makedirs(output_file_dir)
                    
                    total_count += 1
                    print(f"处理文件: {rel_path}")
                    
                    if self.decrypt_file(file_path, output_file):
                        success_count += 1
                        
        print(f"\n批量解密完成: {success_count}/{total_count} 个文件解密成功")

def main():
    parser = argparse.ArgumentParser(description='游戏文件解密工具')
    parser.add_argument('input', help='输入文件或目录路径')
    parser.add_argument('-o', '--output', help='输出文件或目录路径')
    parser.add_argument('-d', '--directory', action='store_true', help='批量处理目录')
    
    args = parser.parse_args()
    
    decryptor = GameDecryptor()
    
    if args.directory:
        decryptor.decrypt_directory(args.input, args.output)
    else:
        decryptor.decrypt_file(args.input, args.output)

if __name__ == "__main__":
    main()
