#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接尝试3DES解密原始数据
"""

import base64
import hashlib
from Crypto.Cipher import DES3
from Crypto.Util.Padding import unpad

def direct_3des_decrypt():
    """
    直接对原始加密字符串进行3DES解密
    """
    
    # 读取原始文件
    with open("gamedata/modcache/HYYM/lua/skill.lua", 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    # 分离长度前缀
    if '@' in content:
        parts = content.split('@', 1)
        expected_length = int(parts[0])
        encrypted_str = parts[1]
        print(f"预期长度: {expected_length}")
        print(f"加密字符串长度: {len(encrypted_str)}")
    else:
        print("没有找到长度前缀")
        return
    
    # 3DES密钥
    des3_key = hashlib.md5("Yh$45Ct@mods".encode('utf-8')).digest()
    print(f"3DES密钥: {des3_key.hex()}")
    
    # 尝试直接Base64解码原始字符串
    print("\n=== 尝试直接Base64解码原始字符串 ===")
    
    try:
        # 添加填充
        padded_str = encrypted_str
        while len(padded_str) % 4 != 0:
            padded_str += '='
        
        raw_data = base64.b64decode(padded_str)
        print(f"Base64解码成功: {len(raw_data)} 字节")
        
        # 确保长度是8的倍数
        if len(raw_data) % 8 != 0:
            truncated_length = (len(raw_data) // 8) * 8
            raw_data = raw_data[:truncated_length]
            print(f"截断到8的倍数: {len(raw_data)} 字节")
        
        # 尝试3DES解密
        cipher = DES3.new(des3_key, DES3.MODE_ECB)
        decrypted_data = cipher.decrypt(raw_data)
        print(f"3DES解密成功: {len(decrypted_data)} 字节")
        
        # 尝试移除填充
        try:
            unpadded_data = unpad(decrypted_data, 8)
            print(f"移除填充后: {len(unpadded_data)} 字节")
            decrypted_data = unpadded_data
        except:
            print("无法移除填充，使用原始数据")
        
        # 尝试解码为字符串
        for encoding in ['utf-8', 'latin-1', 'cp1252']:
            try:
                decoded_str = decrypted_data.decode(encoding)
                print(f"{encoding}解码成功: {len(decoded_str)} 字符")
                print(f"前100字符: {repr(decoded_str[:100])}")
                
                # 检查Lua特征
                if any(keyword in decoded_str for keyword in ['function', 'local', 'end', 'if', 'then', '--']):
                    print(f"✓ 发现Lua关键字！")
                    with open(f"skill_direct_3des_{encoding}.lua", 'w', encoding='utf-8') as f:
                        f.write(decoded_str)
                    print(f"保存到: skill_direct_3des_{encoding}.lua")
                    return decoded_str
                else:
                    print("✗ 未发现Lua关键字")
                    
            except Exception as e:
                print(f"{encoding}解码失败: {e}")
        
    except Exception as e:
        print(f"Base64解码失败: {e}")
    
    # 尝试不同的处理方式
    print("\n=== 尝试其他处理方式 ===")
    
    # 方法1: 先处理字符替换再Base64解码
    processed = encrypted_str.replace('\\', '0').replace('_', '1')
    processed = processed[1:-1]
    processed = processed.replace("/", "").replace("#", "/")
    
    try:
        # 移除最后一个字符并填充
        test_str = processed[:-1]
        while len(test_str) % 4 != 0:
            test_str += '='
        
        raw_data = base64.b64decode(test_str)
        print(f"处理后Base64解码成功: {len(raw_data)} 字节")
        
        # 直接尝试3DES解密
        if len(raw_data) % 8 == 0:
            cipher = DES3.new(des3_key, DES3.MODE_ECB)
            decrypted_data = cipher.decrypt(raw_data)
            
            for encoding in ['utf-8', 'latin-1']:
                try:
                    decoded_str = decrypted_data.decode(encoding)
                    print(f"3DES+{encoding}成功: {len(decoded_str)} 字符")
                    
                    if any(keyword in decoded_str for keyword in ['function', 'local', 'end']):
                        print(f"✓ 3DES+{encoding}发现Lua关键字！")
                        with open(f"skill_processed_3des_{encoding}.lua", 'w', encoding='utf-8') as f:
                            f.write(decoded_str)
                        return decoded_str
                        
                except:
                    continue
        
    except Exception as e:
        print(f"处理后解码失败: {e}")
    
    return None

if __name__ == "__main__":
    result = direct_3des_decrypt()
    if result is None:
        print("\n所有直接3DES解密尝试都失败了")
