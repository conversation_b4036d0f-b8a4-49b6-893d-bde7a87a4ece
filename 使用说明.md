# 游戏文件加密解密工具使用说明

## 功能介绍

这个工具可以帮你一键解密和重新加密游戏文件，支持：
- ✅ Lua脚本文件解密/加密
- ✅ XML配置文件解密/加密
- ✅ 单文件处理
- ✅ 批量目录处理
- ✅ 图形化界面操作
- ✅ 详细的操作日志

## 安装要求

确保你的电脑已安装：
1. Python 3.6 或更高版本
2. 必要的Python库（第一次运行时会自动提示安装）

如果缺少库，请运行：
```bash
pip install pycryptodome
```

## 使用方法

### 方法1：双击启动
直接双击 `启动工具.bat` 文件

### 方法2：命令行启动
```bash
python game_crypto_tool.py
```

## 操作步骤

### 解密文件（从加密变为明文）

1. **选择输入**：
   - 点击"浏览文件"选择单个加密文件
   - 或点击"浏览目录"选择包含加密文件的目录

2. **选择输出目录**：
   - 点击"选择目录"选择解密后文件的保存位置

3. **设置选项**：
   - 操作类型：选择"解密"
   - 文件类型：选择要处理的文件类型（Lua/XML/所有）

4. **开始处理**：
   - 点击"开始处理"按钮
   - 查看进度条和日志信息

### 加密文件（从明文变为加密）

1. **选择输入**：
   - 选择你修改过的明文文件或目录

2. **选择输出目录**：
   - 选择加密后文件的保存位置

3. **设置选项**：
   - 操作类型：选择"加密"
   - 文件类型：选择要处理的文件类型

4. **开始处理**：
   - 点击"开始处理"按钮

## 典型工作流程

### 第一次使用（解密）
1. 选择游戏目录中的加密文件（如 `gamedata/modcache/HYYM/lua/`）
2. 选择输出目录（如 `解密文件/`）
3. 选择"解密"操作
4. 点击"开始处理"
5. 在输出目录中获得可编辑的明文文件

### 修改后重新加密
1. 编辑解密后的文件（如修改 `main_decrypted.lua`）
2. 选择修改后的文件或目录
3. 选择输出目录（建议选择游戏原始目录的备份）
4. 选择"加密"操作
5. 点击"开始处理"
6. 将加密后的文件复制回游戏目录

## 注意事项

⚠️ **重要提醒**：
- 修改游戏文件前请先备份原始文件
- 加密后的文件需要替换游戏目录中的原文件才能生效
- 建议先在备份目录中测试，确认无误后再替换原文件

## 文件命名规则

- 解密时：`原文件名_decrypted.扩展名`
- 加密时：自动移除 `_decrypted` 后缀

## 支持的文件格式

- `.lua` - Lua脚本文件
- `.xml` - XML配置文件
- 其他格式（如果使用相同加密方式）

## 故障排除

### 常见问题

1. **"文件格式不正确"错误**
   - 确保选择的是正确的加密文件
   - 加密文件应该包含长度前缀（如 `549@...`）

2. **"解密失败"错误**
   - 文件可能已经是明文格式
   - 或者使用了不同的加密方式

3. **"找不到文件"错误**
   - 检查文件路径是否正确
   - 确保有足够的读写权限

### 获取帮助

如果遇到问题：
1. 查看操作日志中的详细错误信息
2. 确认文件格式和路径正确
3. 检查Python环境和依赖库

## 技术说明

本工具使用的加密算法：
- **加密方式**：3DES ECB模式
- **密钥来源**：字符串 "Yh$45Ct@mods" 的MD5哈希值
- **编码方式**：Base64编码
- **填充方式**：PKCS7填充

这与游戏中的SaveManager和LuaManager使用的加密方式完全一致。
