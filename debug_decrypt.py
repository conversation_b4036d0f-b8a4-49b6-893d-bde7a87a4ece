#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试解密过程
"""

import base64

def debug_extract_string(encrypted_str):
    """
    调试版本的ExtractString
    """
    print(f"原始输入长度: {len(encrypted_str)}")
    print(f"原始输入: {encrypted_str[:100]}...")

    # 检查是否有长度前缀
    if '@' in encrypted_str:
        parts = encrypted_str.split('@', 1)
        if len(parts) == 2 and parts[0].isdigit():
            length_prefix = parts[0]
            encrypted_str = parts[1]
            print(f"长度前缀: {length_prefix}")
            print(f"移除前缀后长度: {len(encrypted_str)}")
            print(f"移除前缀后: {encrypted_str[:100]}...")
    
    # 按照C#代码的顺序处理
    # 1. 替换特殊字符
    step1 = encrypted_str.replace('\\', '0').replace('_', '1')
    print(f"步骤1 - 替换特殊字符: {step1[:100]}...")
    
    # 2. 移除首尾字符 Substring(1, str.Length - 2)
    if len(step1) >= 3:
        step2 = step1[1:-1]
    else:
        step2 = step1
    print(f"步骤2 - 移除首尾字符: {step2[:100]}...")
    
    # 3. 替换路径分隔符 - 先移除"/"，再把"#"替换为"/"
    step3a = step2.replace("/", "")
    step3 = step3a.replace("#", "/")
    print(f"步骤3a - 移除/: {step3a[:100]}...")
    print(f"步骤3b - #替换为/: {step3[:100]}...")
    
    # 4. Base64解码
    print(f"Base64字符串长度: {len(step3)}")
    print(f"长度模4: {len(step3) % 4}")

    # 如果长度不是4的倍数，添加填充
    if len(step3) % 4 != 0:
        padding = 4 - (len(step3) % 4)
        step3 += '=' * padding
        print(f"添加{padding}个=号填充")

    try:
        decoded_bytes = base64.b64decode(step3)
        print(f"步骤4 - Base64解码成功，字节长度: {len(decoded_bytes)}")
        print(f"前20字节: {decoded_bytes[:20]}")
        
        # 尝试不同编码
        for encoding in ['utf-8', 'latin-1', 'cp1252', 'gbk']:
            try:
                decoded_str = decoded_bytes.decode(encoding)
                print(f"使用{encoding}编码成功，字符串长度: {len(decoded_str)}")
                print(f"前50字符: {repr(decoded_str[:50])}")
                
                # 5. 解压缩字符串 - 按照C#代码逻辑
                print(f"\n开始解压缩...")
                result = []
                i = 0
                while i < len(decoded_str) - 1:
                    char = decoded_str[i]
                    count_char = decoded_str[i + 1]
                    # 按照C#代码: (int)(str[i + 1] - '0')
                    count = ord(count_char) - ord('0')
                    print(f"位置{i}: 字符='{char}' (ord={ord(char)}), 计数字符='{count_char}' (ord={ord(count_char)}), 计数={count}")

                    if 0 <= count <= 9:  # 有效的数字范围
                        result.append(char * count)
                        print(f"  重复{count}次: {repr(char * count)}")
                    else:
                        # 如果不是有效数字，可能需要其他处理
                        print(f"  无效计数{count}，跳过")

                    i += 2
                    if i > 20:  # 只显示前几个
                        print("  ...")
                        break
                
                final_result = ''.join(result)
                print(f"\n最终结果长度: {len(final_result)}")
                print(f"前100字符: {repr(final_result[:100])}")
                
                return final_result
                
            except Exception as e:
                print(f"使用{encoding}编码失败: {e}")
                
    except Exception as e:
        print(f"步骤4 - Base64解码失败: {e}")
        return None

def test_main_lua():
    """测试skill.lua解密"""
    print("=== 调试skill.lua解密 ===")

    # 读取加密的skill.lua
    lua_file = "gamedata/modcache/HYYM/lua/skill.lua"
    try:
        with open(lua_file, 'r', encoding='utf-8') as f:
            encrypted_content = f.read().strip()
            
        result = debug_extract_string(encrypted_content)
        
        if result:
            # 保存结果
            with open("debug_skill_decrypted.lua", 'w', encoding='utf-8') as f:
                f.write(result)
            print(f"\n调试结果已保存到: debug_skill_decrypted.lua")
            
    except Exception as e:
        print(f"测试过程出错: {e}")

if __name__ == "__main__":
    test_main_lua()
